package com.chinamobile.sparrow.springboot.web.controller.sys;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.model.sys.DefaultUser;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.sys.DefaultUserRepository;
import com.google.gson.JsonObject;
import me.chanjar.weixin.common.error.WxErrorException;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Optional;

@Controller
public class DefaultProfileController extends ProfileController {

    public DefaultProfileController(
            DefaultUserRepository userRepository,
            LoginUtil loginUtil
    ) {
        super(userRepository, loginUtil);
    }

    @Override
    public Result<String> save(@RequestBody User user) {
        throw new UnsupportedOperationException();
    }

    @PostMapping(value = "/save/default")
    @ResponseBody
    public Result<String> save(@RequestBody @Validated DefaultUser user) throws InstantiationException, IllegalAccessException {
        User _user = loginUtil.getUser();
        user.setId(_user.getId());
        user.setAccount(_user.getAccount());

        return userRepository.save(user, loginUtil.getUserId());
    }

    @PostMapping(value = "/bind")
    @ResponseBody
    public Result<ImmutablePair<Boolean, String>> binding(@RequestBody JsonObject data) throws WxErrorException {
        String _code = Optional.ofNullable(data.get("code"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);

        Result<ImmutablePair<Boolean, String>> _bind = new Result<>();
        _bind.data = ((DefaultUserRepository) userRepository).binding(_code, loginUtil.getUserId());
        return _bind;
    }

    @PostMapping(value = "/bind/mini-app")
    @ResponseBody
    public Result<Boolean> bind(@RequestBody JsonObject data) throws WxErrorException {
        String _code = Optional.ofNullable(data.get("code"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);

        return ((DefaultUserRepository) userRepository).bindMaOpenIdAndUnionId(_code, loginUtil.getUserId());
    }

}