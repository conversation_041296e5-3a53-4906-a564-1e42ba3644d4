package com.chinamobile.sparrow.springboot.web.controller.sec;

import com.chinamobile.sparrow.domain.infra.code.ErrorCode;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.infra.sec.shiro.realm.UsernamePasswordAuthorizingRealm;
import com.chinamobile.sparrow.domain.infra.sec.shiro.token.WxMaCodeToken;
import com.chinamobile.sparrow.domain.infra.sec.shiro.token.WxMaPhoneNumberToken;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.sec.VerificationCodeRepository;
import com.chinamobile.sparrow.domain.service.cmpassport.Facade;
import com.chinamobile.sparrow.domain.service.wx.cp.AccessFacade;
import com.google.gson.JsonObject;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.Subject;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;

@Controller
@RequestMapping(value = "sec")
@ErrorCode(module = "001")
public class DefaultLoginController extends LoginController {

    final boolean captcha;

    final com.chinamobile.sparrow.domain.service.wx.ma.AccessFacade wxMaAccessFacade;

    public DefaultLoginController(
            boolean captcha,
            String rsaPublicKey,
            String rsaPrivateKey,
            VerificationCodeRepository verificationCodeRepository,
            Facade cmpassportFacade,
            AccessFacade wxCpAccessFacade,
            AccessFacade yzyAccessFacade,
            com.chinamobile.sparrow.domain.service.wx.ma.AccessFacade wxMaAccessFacade,
            LoginUtil loginUtil
    ) {
        super(rsaPublicKey, rsaPrivateKey, verificationCodeRepository, cmpassportFacade, wxCpAccessFacade, yzyAccessFacade, loginUtil);
        this.wxMaAccessFacade = wxMaAccessFacade;
        this.captcha = captcha;
    }

    @Override
    public Result<String> loginByPassword(HttpServletRequest request, @RequestBody JsonObject data) throws Exception {
        return loginUtil.login(request, data, UsernamePasswordAuthorizingRealm.REALM_TYPE, captcha, false);
    }

    @PostMapping(value = "/login/mini-app")
    @ResponseBody
    public Result<User> loginByMiniAppSessionCode(HttpServletRequest request, @RequestBody JsonObject data) throws UnsupportedEncodingException {
        Result<User> _user = new Result<>();

        Subject _subject = SecurityUtils.getSubject();
        if (_subject.isAuthenticated() || _subject.isRemembered()) {
            _user.setCode(Result.ENUM_ERROR.P, 11);
            return _user;
        }

        String _code = data.get("code").getAsString();
        WxMaCodeToken _token = new WxMaCodeToken(_code, request.getRemoteHost());
        loginUtil.login(request, _token, null);

        _user.data = loginUtil.getUser();
        _user.data.setPassword(null);
        _user.data.setIsOnline(false);
        return _user;
    }

    @PostMapping(value = "/login/mini-app/mp")
    @ResponseBody
    public Result<User> loginByMiniApp(HttpServletRequest request, @RequestBody JsonObject data) throws UnsupportedEncodingException {
        Result<User> _user = new Result<>();

        Subject _subject = SecurityUtils.getSubject();
        if (_subject.isAuthenticated() || _subject.isRemembered()) {
            _user.setCode(Result.ENUM_ERROR.P, 11);
            return _user;
        }

        String _code = data.get("code").getAsString();
        String _encryptedData = data.get("encryptedData").getAsString();
        String _iv = data.get("iv").getAsString();

        WxMaPhoneNumberToken _token = new WxMaPhoneNumberToken(_code, _encryptedData, _iv, request.getRemoteHost());
        loginUtil.login(request, _token, null);

        _user.data = loginUtil.getUser();
        _user.data.setPassword(null);
        _user.data.setIsOnline(false);
        return _user;
    }

}