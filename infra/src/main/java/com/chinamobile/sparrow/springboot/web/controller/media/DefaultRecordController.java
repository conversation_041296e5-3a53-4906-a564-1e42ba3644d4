package com.chinamobile.sparrow.springboot.web.controller.media;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.model.media.Media;
import com.chinamobile.sparrow.domain.repository.media.AbstractMediaRepository;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

@Controller
public class DefaultRecordController extends RecordController {

    public DefaultRecordController(AbstractMediaRepository mediaRepository, LoginUtil loginUtil) {
        super(mediaRepository, loginUtil);
    }

    @Override
    public ResponseEntity<StreamingResponseBody> writeToResponse(String id, boolean asAttachment) throws Exception {
        Result<Media> _record = mediaRepository.get(id, null);
        if (!_record.isOK()) {
            throw new Exception(_record.message);
        }

        Result<InputStream> _input = mediaRepository.getInputStream(_record.data);
        if (!_input.isOK()) {
            throw new Exception(_input.message);
        }

        ResponseEntity.BodyBuilder _builder = ResponseEntity.ok();

        if (asAttachment) {
            _builder = _builder
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=" + URLEncoder.encode(_record.data.getName(), StandardCharsets.UTF_8.name()))
                    .contentType(MediaType.APPLICATION_OCTET_STREAM);
        }

        return _builder.body(output -> FileCopyUtils.copy(_input.data, output));
    }

}