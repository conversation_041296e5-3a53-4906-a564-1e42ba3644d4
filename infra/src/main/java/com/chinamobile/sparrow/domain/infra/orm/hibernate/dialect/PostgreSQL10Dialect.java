package com.chinamobile.sparrow.domain.infra.orm.hibernate.dialect;

import org.hibernate.dialect.function.StandardSQLFunction;
import org.hibernate.type.StandardBasicTypes;

public class PostgreSQL10Dialect extends org.hibernate.dialect.PostgreSQL10Dialect {

    public PostgreSQL10Dialect() {
        super();

        registerFunction("dateFormat", new StandardSQLFunction("to_char", StandardBasicTypes.STRING));

        registerFunction("datePart", new StandardSQLFunction("date_part", StandardBasicTypes.INTEGER));
    }

}