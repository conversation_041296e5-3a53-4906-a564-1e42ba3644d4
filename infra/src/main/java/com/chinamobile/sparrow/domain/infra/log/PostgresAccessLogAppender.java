package com.chinamobile.sparrow.domain.infra.log;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.classic.spi.IThrowableProxy;
import ch.qos.logback.classic.spi.StackTraceElementProxy;
import ch.qos.logback.core.db.DBHelper;
import ch.qos.logback.core.db.dialect.SQLDialectCode;
import com.chinamobile.sparrow.domain.util.DateUtil;
import com.chinamobile.sparrow.domain.util.IdWorker;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.sql.*;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

public class PostgresAccessLogAppender extends AccessLogAppender {

    @Override
    public void append(ILoggingEvent eventObject) {
        Connection connection = null;
        PreparedStatement insertStatement = null;

        try {
            connection = connectionSource.getConnection();
            connection.setAutoCommit(false);
            if (cnxSupportsGetGeneratedKeys) {
                String EVENT_ID_COL_NAME = "EVENT_ID";
                if (connectionSource.getSQLDialectCode() == SQLDialectCode.POSTGRES_DIALECT) {
                    EVENT_ID_COL_NAME = EVENT_ID_COL_NAME.toLowerCase();
                }

                insertStatement = connection.prepareStatement(getInsertSQL(eventObject.getLevel()), new String[]{EVENT_ID_COL_NAME});
            } else {
                insertStatement = connection.prepareStatement(getInsertSQL(eventObject.getLevel()));
            }

            synchronized (this) {
                table(connection, eventObject.getLevel());

                subAppend(eventObject, connection, insertStatement);
            }

            connection.commit();
        } catch (Throwable e) {
            addError("problem appending event", e);
        } finally {
            DBHelper.closeStatement(insertStatement);
            DBHelper.closeConnection(connection);
        }
    }

    @Override
    protected void bindLoggingEventWithInsertStatement(ILoggingEvent iLoggingEvent, PreparedStatement preparedStatement) throws SQLException {
        Map<String, String> _mdc = iLoggingEvent.getMDCPropertyMap();

        String _transactionId = _mdc.getOrDefault("transactionId", null);
        String _stack = _mdc.getOrDefault("stack", null);
        String _request = _mdc.getOrDefault("request", null);
        String _response = _mdc.getOrDefault("response", null);
        String _ip = _mdc.getOrDefault("ip", null);
        String _actorId = _mdc.getOrDefault("actorId", null);
        String _realmType = _mdc.getOrDefault("realmType", null);
        String _startTime = _mdc.getOrDefault("startTime", null);
        String _endTime = _mdc.getOrDefault("endTime", null);

        preparedStatement.setLong(ID_INDEX, IdWorker.getInstance().nextId());
        preparedStatement.setString(LEVEL_INDEX, iLoggingEvent.getLevel().toString());
        preparedStatement.setString(TRANSACTION_ID_INDEX, _transactionId);
        preparedStatement.setString(STACK_INDEX, _stack);
        preparedStatement.setString(METHOD_INDEX, iLoggingEvent.getMessage());
        preparedStatement.setString(REQUEST_INDEX, _request);
        preparedStatement.setString(RESPONSE_INDEX, _response);
        preparedStatement.setString(IP_INDEX, _ip);
        preparedStatement.setString(ACTOR_ID_INDEX, _actorId);
        preparedStatement.setString(REALM_TYPE_INDEX, _realmType);

        SimpleDateFormat _format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        Date _time;
        try {
            _time = _format.parse(_startTime);
            preparedStatement.setTimestamp(START_TIME_INDEX, new Timestamp(_time.getTime()));
        } catch (ParseException e) {
            System.out.println(e);
        }

        try {
            _time = _format.parse(_endTime);
            preparedStatement.setTimestamp(END_TIME_INDEX, new Timestamp(_time.getTime()));
        } catch (ParseException e) {
            System.out.println(e);
        }

        if (Level.INFO.equals(iLoggingEvent.getLevel())) {
            String _responseCode = _mdc.getOrDefault("responseCode", null);
            preparedStatement.setString(RESPONSE_CODE_INDEX, _responseCode);
        } else if (Level.ERROR.equals(iLoggingEvent.getLevel())) {
            IThrowableProxy _throwable = iLoggingEvent.getThrowableProxy();

            preparedStatement.setString(EXCEPTION_INDEX, _throwable.getMessage());

            StringBuilder _str = new StringBuilder();
            for (StackTraceElementProxy i : _throwable.getStackTraceElementProxyArray()) {
                _str.append(i.getSTEAsString()).append("\n");
            }
            preparedStatement.setString(STACK_TRACE_INDEX, _str.toString());
        }
    }


    @Override
    void table(Connection connection, Level level) throws SQLException {
        if (CollectionUtils.isEmpty(tableNames)) {
            ResultSet _tables = connection.getMetaData().getTables(connection.getCatalog(), null, null, new String[]{"TABLE"});
            while (_tables.next()) {
                String _tableName = _tables.getString("TABLE_NAME");
                if (_tableName.startsWith(INFO_LOG_PREFIX) || _tableName.startsWith(ERROR_LOG_PREFIX)) {
                    tableNames.add(_tableName);
                }
            }
        }

        String _suffix = DateUtil.toString(new Date(), "yyyyMM"), _tableName = null;
        if (level == Level.INFO) {
            _tableName = INFO_LOG_PREFIX + _suffix;
        } else if (level == Level.ERROR) {
            _tableName = ERROR_LOG_PREFIX + _suffix;
        }
        if (!StringUtils.hasLength(_tableName)) {
            return;
        }

        if (!tableNames.contains(_tableName)) {
            try (Statement statement = connection.createStatement()) {
                if (Level.INFO == level) {
                    // 创建表
                    String _sql = "CREATE TABLE " + _tableName + " (id BIGINT NOT NULL PRIMARY KEY, level VARCHAR(10), transactionId VARCHAR(32), stack VARCHAR(255), method TEXT, request TEXT, response TEXT, ip VARCHAR(128), actorId VARCHAR(36), realmType VARCHAR(32), startTime TIMESTAMP, endTime TIMESTAMP, responseCode VARCHAR(255))";
                    statement.execute(_sql);

                    // 创建索引
                    _sql = "CREATE INDEX index_response_code ON " + _tableName + " (responseCode)";
                    statement.execute(_sql);
                    _sql = "CREATE INDEX index_actor_id ON " + _tableName + " (actorId)";
                    statement.execute(_sql);
                    _sql = "CREATE INDEX index_start_time ON " + _tableName + " (startTime)";
                    statement.execute(_sql);
                    _sql = "CREATE INDEX index_actor_id_start_time ON " + _tableName + " (actorId, startTime)";
                    statement.execute(_sql);
                } else if (Level.ERROR == level) {
                    // 创建表
                    String _sql = "CREATE TABLE " + _tableName + " (id BIGINT NOT NULL PRIMARY KEY, level VARCHAR(10), transactionId VARCHAR(32), stack VARCHAR(255), method TEXT, request TEXT, response TEXT, ip VARCHAR(128), actorId VARCHAR(18), realmType VARCHAR(32), startTime TIMESTAMP, endTime TIMESTAMP, exception TEXT, stackTrace TEXT)";
                    statement.execute(_sql);

                    // 创建索引
                    _sql = "CREATE INDEX index_actor_id ON " + _tableName + " (actorId)";
                    statement.execute(_sql);
                    _sql = "CREATE INDEX index_start_time ON " + _tableName + " (startTime)";
                    statement.execute(_sql);
                }
            }

            tableNames.add(_tableName);
        }
    }

}