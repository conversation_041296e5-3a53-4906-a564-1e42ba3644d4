package com.chinamobile.sparrow.domain.repository.sys;

import com.chinamobile.sparrow.domain.infra.log.AbstractLog;
import com.chinamobile.sparrow.domain.infra.orm.jinq.PostgreSQLFunctions;
import com.chinamobile.sparrow.domain.model.sys.InfoLog;
import com.chinamobile.sparrow.domain.util.DateUtil;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.jinq.tuples.Pair;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManagerFactory;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Transactional(readOnly = true)
public class DefaultStatisticService extends StatisticService {

    public DefaultStatisticService(EntityManagerFactory entityManagerFactory, JinqJPAStreamProvider jinqJPAStreamProvider, UserRepository userRepository) {
        super(entityManagerFactory, jinqJPAStreamProvider, userRepository);
    }

    /**
     * 每日活跃用户数
     *
     * @param begin
     * @param end
     * @param method
     * @param individual
     * @return
     */
    @Override
    public List<Pair<Date, Long>> dau(Date begin, Date end, String method, boolean individual) {
        if (begin == null) {
            begin = new Date();
        }
        Date _begin = DateUtil.getDate(begin);

        end = DateUtil.addDays(end == null ? begin : end, 1);
        Date _end = DateUtil.getDate(end);

        JinqStream<InfoLog> _query = streamInfoLogs(_begin, _end, method);

        List<Pair<String, Long>> _logs;
        if (individual) {
            _logs = _query.group(i -> PostgreSQLFunctions.dateFormat(i.getStartTime(), "yyyy-MM-dd"), (key, stream) -> stream.select(AbstractLog::getActorId).distinct().count())
                    .toList();
        } else {
            _logs = _query.group(i -> PostgreSQLFunctions.dateFormat(i.getStartTime(), "yyyy-MM-dd"), (key, stream) -> stream.count())
                    .toList();
        }

        List<Pair<Date, Long>> _counts = new ArrayList<>();
        for (Date i = _begin; i.before(_end); i = DateUtil.addDays(i, 1)) {
            Date _i = i;
            Pair<String, Long> _log = _logs.stream()
                    .filter(j -> Objects.equals(DateUtil.toString(_i, "yyyy-MM-dd"), j.getOne()))
                    .findFirst().orElse(null);
            _counts.add(_log == null ? new Pair<>(i, 0L) : new Pair<>(DateUtil.from(_log.getOne(), "yyyy-MM-dd"), _log.getTwo()));
        }
        return _counts;
    }

    /**
     * 活跃分布
     *
     * @param begin
     * @param end
     * @param method
     * @return
     */
    public List<Pair<Integer, Long>> trend(Date begin, Date end, String method) {
        if (begin == null) {
            begin = new Date();
        }
        Date _begin = DateUtil.getDate(begin);

        end = DateUtil.addDays(end == null ? begin : end, 1);
        Date _end = DateUtil.getDate(end);

        List<Pair<Pair<Integer, Integer>, Long>> _query = streamInfoLogs(_begin, _end, method)
                .group(i -> new Pair<>(PostgreSQLFunctions.datePart("hour", i.getStartTime()), PostgreSQLFunctions.datePart("minute", i.getStartTime()) < 30 ? 0 : 30), (i, stream) -> stream.count())
                .toList();

        List<Pair<Integer, Long>> _counts = new ArrayList<>();
        _query.stream()
                .collect(Collectors.groupingBy(i -> i.getOne().getOne() * 60 + i.getOne().getTwo()))
                .forEach((key, value) -> _counts.add(new Pair<>(key, value.stream()
                        .map(Pair::getTwo)
                        .reduce(0L, Long::sum))));
        return _counts;
    }

}