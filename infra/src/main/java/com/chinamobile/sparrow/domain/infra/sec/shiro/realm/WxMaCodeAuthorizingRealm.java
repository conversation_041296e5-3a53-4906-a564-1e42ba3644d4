package com.chinamobile.sparrow.domain.infra.sec.shiro.realm;

import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.token.WxMaCodeToken;
import com.chinamobile.sparrow.domain.model.sys.DefaultUser;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.sec.PermissionRepository;
import com.chinamobile.sparrow.domain.repository.sec.RoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.DefaultUserRepository;
import com.chinamobile.sparrow.domain.repository.sys.DepartmentRepository;
import com.chinamobile.sparrow.domain.service.wx.ma.DefaultAccessFacade;
import com.chinamobile.sparrow.domain.util.IdWorker;
import me.chanjar.weixin.common.error.WxErrorException;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.AuthenticationInfo;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.SimpleAuthenticationInfo;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

public class WxMaCodeAuthorizingRealm extends AuthorizationRealm {

    final String DEPARTMENT_NAME = "小程序用户";

    final String departmentId;
    final DefaultAccessFacade accessFacade;

    public WxMaCodeAuthorizingRealm(
            DefaultUserRepository userRepository,
            DepartmentRepository<Department> departmentRepository,
            RoleRepository roleRepository,
            PermissionRepository permissionRepository,
            DefaultAccessFacade accessFacade
    ) {
        super(null, userRepository, roleRepository, permissionRepository, null);

        List<Department> _departments = departmentRepository.find(null, Collections.singletonList(DEPARTMENT_NAME), null, true, null);
        if (CollectionUtils.isEmpty(_departments)) {
            Department _department = new Department();
            _department.setName(DEPARTMENT_NAME);
            _department.setCode(_department.getId());
            _department.setFullName(DEPARTMENT_NAME);
            _department.setLevel(1);
            departmentRepository.add(_department, null);

            this.departmentId = _department.getId();
        } else {
            this.departmentId = _departments.get(0).getId();
        }

        this.accessFacade = accessFacade;
    }

    @Override
    public boolean supports(AuthenticationToken token) {
        return token instanceof WxMaCodeToken;
    }

    @Override
    protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken token) throws AuthenticationException {
        if (token.getCredentials() == null) {
            return null;
        }

        String _code = new String((char[]) token.getCredentials());

        WxMaJscode2SessionResult _session;
        try {
            _session = accessFacade.getSession(_code);
        } catch (WxErrorException e) {
            logger.error("小程序OAuth2登录失败", e);

            throw new AuthenticationException(e);
        }


        Result<DefaultUser> _user = ((DefaultUserRepository) userRepository).getBriefInWxMa(_session.getOpenid(), null, true);
        // 自动注册用户
        if (!_user.isOK()) {
            DefaultUser _temp = new DefaultUser();
            _temp.setId(_session.getOpenid());
            _temp.setAccount(_session.getOpenid());
            _temp.setName(String.format("用户%s", IdWorker.getInstance().nextId()));
            _temp.setMaOpenId(_session.getOpenid());
            _temp.setUnionId(_session.getUnionid());
            _temp.setDeptId(departmentId);

            userRepository.add(_temp, null);

            _user.data = _temp;
        }

        return new SimpleAuthenticationInfo(_user.data, token.getCredentials(), this.getClass().toString());
    }

    @Override
    protected String getPrincipal(AuthenticationToken token) {
        return null;
    }

    @Override
    protected AuthenticationInfo getAuthenticationInfo(AuthenticationToken token, User user) {
        return null;
    }

}