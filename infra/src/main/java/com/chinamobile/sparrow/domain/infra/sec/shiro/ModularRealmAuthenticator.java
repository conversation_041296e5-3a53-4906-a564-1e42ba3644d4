package com.chinamobile.sparrow.domain.infra.sec.shiro;

import com.chinamobile.sparrow.domain.infra.sec.shiro.realm.*;
import com.chinamobile.sparrow.domain.infra.sec.shiro.token.*;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.AuthenticationInfo;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authz.UnauthenticatedException;
import org.apache.shiro.realm.Realm;

import java.util.ArrayList;
import java.util.List;

public class ModularRealmAuthenticator extends org.apache.shiro.authc.pam.ModularRealmAuthenticator {

    protected final ConfigUserAuthorizingRealm configUserAuthorizingRealm;
    protected final UsernamePasswordAuthorizingRealm usernamePasswordAuthorizingRealm;
    protected final SMSAuthorizingRealm smsAuthorizingRealm;
    protected final CMPassportAuthorizingRealm cmPassportAuthorizingRealm;
    protected final WxCpAuthorizingRealm wxCpAuthorizingRealm;
    protected final WxMaCodeAuthorizingRealm wxMaCodeAuthorizingRealm;
    protected final WxMaPhoneNumberAuthorizingRealm wxMaPhoneNumberAuthorizingRealm;
    protected final YzyAuthorizingRealm yzyAuthorizingRealm;

    public ModularRealmAuthenticator(
            ConfigUserAuthorizingRealm configUserAuthorizingRealm,
            UsernamePasswordAuthorizingRealm usernamePasswordAuthorizingRealm,
            SMSAuthorizingRealm smsAuthorizingRealm,
            CMPassportAuthorizingRealm cmPassportAuthorizingRealm,
            WxCpAuthorizingRealm wxCpAuthorizingRealm,
            WxMaCodeAuthorizingRealm wxMaCodeAuthorizingRealm,
            WxMaPhoneNumberAuthorizingRealm wxMaPhoneNumberAuthorizingRealm,
            YzyAuthorizingRealm yzyAuthorizingRealm
    ) {
        this.configUserAuthorizingRealm = configUserAuthorizingRealm;
        this.usernamePasswordAuthorizingRealm = usernamePasswordAuthorizingRealm;
        this.smsAuthorizingRealm = smsAuthorizingRealm;
        this.cmPassportAuthorizingRealm = cmPassportAuthorizingRealm;
        this.wxCpAuthorizingRealm = wxCpAuthorizingRealm;
        this.wxMaCodeAuthorizingRealm = wxMaCodeAuthorizingRealm;
        this.wxMaPhoneNumberAuthorizingRealm = wxMaPhoneNumberAuthorizingRealm;
        this.yzyAuthorizingRealm = yzyAuthorizingRealm;
    }

    @Override
    protected AuthenticationInfo doAuthenticate(AuthenticationToken authenticationToken) throws AuthenticationException {

        assertRealmsConfigured();

        List<Realm> _realms = new ArrayList<>();
        if (authenticationToken instanceof ConfigUserToken && configUserAuthorizingRealm != null) {
            _realms.add(configUserAuthorizingRealm);
        } else if (authenticationToken instanceof SMSToken && smsAuthorizingRealm != null) {
            _realms.add(smsAuthorizingRealm);
        } else if (authenticationToken instanceof CMPassportToken && cmPassportAuthorizingRealm != null) {
            _realms.add(cmPassportAuthorizingRealm);
        } else if (authenticationToken instanceof YzyToken && yzyAuthorizingRealm != null) {
            _realms.add(yzyAuthorizingRealm);
        } else if (authenticationToken instanceof WxCpToken && wxCpAuthorizingRealm != null) {
            _realms.add(wxCpAuthorizingRealm);
        } else if (authenticationToken instanceof WxMaCodeToken && wxMaCodeAuthorizingRealm != null) {
            _realms.add(wxMaCodeAuthorizingRealm);
        } else if (authenticationToken instanceof WxMaPhoneNumberToken && wxMaPhoneNumberAuthorizingRealm != null) {
            _realms.add(wxMaPhoneNumberAuthorizingRealm);
        } else if (usernamePasswordAuthorizingRealm != null) {
            _realms.add(usernamePasswordAuthorizingRealm);
        }

        if (_realms.isEmpty()) {
            throw new UnauthenticatedException();
        } else if (_realms.size() == 1) {
            return doSingleRealmAuthentication(_realms.iterator().next(), authenticationToken);
        } else {
            return doMultiRealmAuthentication(_realms, authenticationToken);
        }
    }

}