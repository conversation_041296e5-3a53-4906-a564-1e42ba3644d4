package com.chinamobile.sparrow.domain.repository.sys;

import com.chinamobile.sparrow.domain.model.sys.Page;
import com.chinamobile.sparrow.domain.repository.sec.PermissionRepository;
import org.jinq.jpa.JinqJPAStreamProvider;

import javax.persistence.EntityManagerFactory;
import java.util.List;
import java.util.Map;

public class DefaultPageRepository extends PageRepository {

    public DefaultPageRepository(
            EntityManagerFactory entityManagerFactory,
            JinqJPAStreamProvider jinqJPAStreamProvider,
            PermissionRepository permissionRepository
    ) {
        super(entityManagerFactory, jinqJPAStreamProvider, permissionRepository);
    }

    @Override
    public Map<String, String[]> readPagePermissionMappings() {
        Map<String, String[]> _maps = super.readPagePermissionMappings();

        // 知识库
        _maps.put("/llm/datasets", null);
        _maps.put("/llm/dataset/edit", null);

        // 应用
        _maps.put("/llm/applications", null);
        _maps.put("/llm/application/dataset", null);
        _maps.put("/llm/application/dataset/edit", null);
        _maps.put("/llm/application/link", null);
        _maps.put("/llm/application/link/edit", null);
        _maps.put("/llm/application/prompt", null);
        _maps.put("/llm/application/prompt/edit", null);
        _maps.put("/llm/application/workflow", null);
        _maps.put("/llm/application/workflow/edit", null);

        // 模型管理
        _maps.put("/sys/llm/model", new String[]{"sys:llm:model:index"});
        _maps.put("/sys/llm/model/edit", new String[]{"sys:llm:model:edit"});

        // 知识库管理
        _maps.put("/sys/llm/dataset", new String[]{"sys:llm:dataset:index"});
        _maps.put("/sys/llm/dataset/authorization", new String[]{"sys:llm:dataset:authorization"});

        // 应用管理
        _maps.put("/sys/llm/application", new String[]{"sys:llm:application:index"});
        _maps.put("/sys/llm/application/authorization", new String[]{"sys:llm:application:authorization"});

        return _maps;
    }

    @Override
    protected void customize(List<Page> records) {
        if (records.stream().noneMatch(i -> "对话".equals(i.getTitle()) && i.getLevel() == 1)) {
            add("对话", null, "CommentOutlined", "/llm/conversation", 1, 1, true, false);
        }
        if (records.stream().noneMatch(i -> "数智人".equals(i.getTitle()) && i.getLevel() == 1)) {
            add("数智人", null, "icon-jiqiren", "/llm/applications", 1, 2, true, false);
        }
        if (records.stream().noneMatch(i -> "知识库".equals(i.getTitle()) && i.getLevel() == 1)) {
            add("知识库", null, "icon-shujuku", "/llm/datasets", 1, 3, true, false);
        }

        if (records.stream().noneMatch(i -> "业务管理".equals(i.getTitle()) && i.getLevel() == 1)) {
            add("业务管理", null, "AppstoreAddOutlined", 1, 4, true);
        }

        super.customize(records);
    }

    @Override
    protected void updateBuildIn(List<Page> records) {
        // 取消首页
        // update(records, "/", null, "主页", "HomeOutlined", 1, 1);

        // 业务管理
        update(records, "/sys/llm/model", "业务管理", "大模型管理", "icon-damoxing", 2, 1);

        update(records, "/sys/llm/application", "业务管理", "应用管理", "icon-jiqiren", 2, 2);

        update(records, "/sys/llm/dataset", "业务管理", "知识库管理", "icon-shujuku", 2, 3);

        update(records, "/sys/stat", "运营统计", "活跃统计", "DotChartOutlined", 2, 1);

        // 系统管理
        update(records, "/sec/role", "系统管理", "角色管理", "SecurityScanOutlined", 2, 2);

        update(records, "/sys/contact", "用户管理", "通讯录", "ContactsOutlined", 3, 1);
        update(records, "/sec/online", "用户管理", "在线管理", "DesktopOutlined", 3, 2);

        update(records, "/sys/job", "系统管理", "作业管理", "ScheduleOutlined", 2, 3);
        update(records, "/sys/dictionary", "系统管理", "字典管理", "ControlOutlined", 2, 4);

        update(records, "/sys/sms/outbox", "短信管理", "发件箱", "UploadOutlined", 3, 1);

        update(records, "/media", "系统管理", "文件管理", "FileOutlined", 2, 6);

        update(records, "/sys/log/info", "日志管理", "访问日志", "FileSearchOutlined", 3, 1);
        update(records, "/sys/log/error", "日志管理", "错误日志", "BugOutlined", 3, 2);

        update(records, "/sec/role/edit", null, "角色编辑", null, null, null);
        update(records, "/sec/role/authorization", null, "角色授权", null, null, null);
        update(records, "/sec/role/appointment", null, "角色任命", null, null, null);
        update(records, "/sys/user/edit", null, "用户编辑", null, null, null);
        update(records, "/sys/dictionary/edit", null, "字典项目编辑", null, null, null);
    }

}