server:
  port: 8080
  servlet:
    context-path:

  # 错误页
  error:
    path: /error
    include-binding-errors: always
    include-exception: true
    include-message: always
    include-stacktrace: always

spring:
  application:
    name: 天枢AI工作站

  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration
      - com.chinamobile.sparrow.springboot.web.autoconfigure.DataSourceAutoConfiguration

  datasource:
    main:
      type: com.zaxxer.hikari.HikariDataSource
      driver-class-name: org.postgresql.Driver
      jdbc-url: *********************************
      username: postgres
      password: postgres
      maximum-pool-size: 16
      config: shardingsphere.yml
      properties:
        hibernate:
          allow_update_outside_transaction: true
          cache:
            use_query_cache: false
            use_second_level_cache: false
          default_schema: public
          dialect: com.chinamobile.sparrow.domain.infra.orm.hibernate.dialect.PostgreSQL10Dialect
          hbm2ddl:
            auto: update
          show_sql: true
      packages: com.chinamobile.sparrow.domain.model, com.chinamobile.sparrow.ai.model, com.chinamobile.si.dubhe.model

    quartz:
      type: com.zaxxer.hikari.HikariDataSource
      driver-class-name: org.postgresql.Driver
      jdbc-url: *********************************
      username: postgres
      password: postgres
      maximum-pool-size: 2

  gson:
    date-format: yyyy-MM-dd HH:mm:ss
    serialize-nulls: true

  jmx:
    enabled: false

  messages:
    basename: i18n/messages
    encoding: UTF-8

  mvc:
    converters:
      preferred-json-mapper: gson

  pid:
    file: app.pid

  quartz:
    job-store-type: jdbc
    jdbc:
      # initialize-schema: always
      initialize-schema: never
    properties:
      org:
        quartz:
          jobStore:
            driverDelegateClass: org.quartz.impl.jdbcjobstore.PostgreSQLDelegate
            useProperties: true
    wait-for-jobs-to-complete-on-shutdown: true

  redis:
    host: localhost
    port: 6379
    password:
    lettuce:
      pool:
        max-active: 64

  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB

# 日志
logging:
  config: logback-spring.xml

shiro:
  enabled: true
  annotations:
    enabled: true
  async:
    enabled: true
  web:
    enabled: true
  session-id-cookie:
    name: dubhe-session-id
    http-only: true
    same-site: Strict
    secure: false
  remember-me-cookie:
    name: dubhe-remember-me
    max-age: 2592000
    http-only: true
    same-site: Strict
    secure: false
  session:
    timeout: 1800
    validation-interval: 3600
  filter-chain-definition:
    - /sec/login/cancel, user
    - /sec/login/**, anon
    - /error, anon
    - /app/init, anon
    - /app/layout/header, anon
    - /media/read, anon
    - /media/read/force-partial-content, anon
    - /util/cmpassport/app-id, anon
    - /util/validation/jquery, anon

    # 其它
    - /**, user
  redis-session-dao:
    enabled: false
    key-template: "dubhe:shiro:session:%s"

# 切面
aspect:
  log:
    enabled: true
    max-length: 128000

# 文件系统存储
file:
  dir:
    env:
    default:
  extension:
    allowed: txt,doc,docx,xls,xlsx,ppt,pptx,ofd,pdf,avif,bmp,gif,ico,jfif,jpeg,jpg,jxl,pjp,pjpeg,png,svg,svgz,tif,tiff,webp,xbm,asx,avi,m4v,mov,mp4,mpeg,mpg,ogm,ogv,webm,wmv,rar,zip
    forbidden:
  part:
    size: 1048576

# 作业
job:
  packages: com.chinamobile

# okhttp连接池
okhttp:
  connection-pool:
    default:
      keep-alive: 5
      max-idle: 5

# 对象存储
s3:
  enabled: false
  endpoint: https://eos-shanghai-1.cmecloud.cn
  access-key:
  secret-key:
  bucket:
    env: dubhe-dev
    default: default

# 安全
sec:
  username:
  password-constraint: ^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[\W_])[A-Za-z\d\W_]{8,}$
  captcha: true
  login:
    url:
    max-attempts: 5
  oauth2:
    client-id:
    client-secret:
  rsa:
    default:
      public-key: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCwiByu6C74TYTenPSdVP2jdHjgSZZuab16QUWyiseYsXq2FSt+eDwOr9eY5yuv80vmCou0diaL3h7UGUhZdzgDVLRFL2qUwbrvrmdBmkmWHCCpndQqcTLeQM+29wzWT/Nk9qWiePneLKpIXHtEm60Lx4/UTL88KBaAHb7I4s8swwIDAQAB
      private-key: MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBALCIHK7oLvhNhN6c9J1U/aN0eOBJlm5pvXpBRbKKx5ixerYVK354PA6v15jnK6/zS+YKi7R2JoveHtQZSFl3OANUtEUvapTBuu+uZ0GaSZYcIKmd1CpxMt5Az7b3DNZP82T2paJ4+d4sqkhce0SbrQvHj9RMvzwoFoAdvsjizyzDAgMBAAECgYBorT085ca8S9Mm4bEW/gesfesTK+98p0zIip3Wgf5a55qZD7KLuqX6G4DfVOyN19nAna47ove2Zk6nfBpvmobmTpne1mvnIJw68bsBZ0KRcjVyq10EWRVdmPNXm0WjXLxijE1WUBE7hF4DfLacVceOKYM3cFYAKk+ZRPza2zqJyQJBANb5yQSA1b0kBkCt973ZV151L+sC2yXx4Lv7c97Yg3ZtZeFzTCGIC5zKnCsUCkKzkE7KYkUsg6yyW06Tc3SXmC8CQQDSODgkcG6nE5CihglIAdvh7bbdjaaNajKeiMclKRnOLLg6lPgVdAaHCUmPFadRFwV7LFmxALktoFgHyek3PbutAkBDoNbdJM6YruGMrW3Xeq0imQkXDJH2kO4bFRKxkzlgIzA+yW+0yMCmdZLFdot3yldmggKCMmvGA2H4CSj0D9CfAkB/y7+Nn3AVT9aQhs1jogWrDlkpzPAgbhwBtoLiO189sH/61Q8P8634L5QxUOeba1RgsfCAo0Dv1NdPPNbzRystAkEAinG+oELF+bjgReMiL9Wl8JrDjjJvO2xWuvSurtwRj0nlNTnET5VOH8ihX9eNjp0dyaZYCCcuKCuM3co6wYuwIQ==

  sms:
    code-length: 6
    expires-in: 1
    template:

thread-pool:
  # 默认线程池
  default:
    core: 8
    max: 16

web:
  # 跨域
  cors:
    allow-credentials: true
    allowed-headers:
      - "*"
    allowed-methods:
      - GET
      - POST
      - OPTIONS
    allowed-origins:
      - http://localhost
      - http://localhost:8000
    exposed-headers:
      - "Content-Disposition"
    max-age: 3600

# 中国移动手机号码认证
cmpassport:
  enabled: false
  base-url: https://token.cmpassport.com:8300
  app-id:
  app-key:
  rsa:
    public-key:
    private-key:

# 移动云MAS
mas:
  enabled: false
  sms:
    base-url: https://*************:28888
    ec-name:
    ap-id:
    secret-key:
    sign:

# 企信通
qxt:
  enabled: false
  base-url: http://*************
  eid:
  userId:
  password:
  encrypt-key:
  port:
    default:

# 高德地图
amap:
  api-key:
  js:
    key:
    code:

# 腾讯地图
qqmap:
  js-key:

# Bing搜索
bing:
  search:
    base-url: https://api.bing.microsoft.com/v7.0
    api-key:
    count: 10

# Cognitive Services
cognitive:
  speech:
    key:
    region:

# dify
dify:
  base-url: http://*************:11016/dify
  console:
    username: <EMAIL>
    password: Hg@123456
  api:
    key: dataset-ZwHf9xkns3UZIz6mdBli09R7
  general-chat-app-id: 600d9efc-4502-46c6-8bcf-0819bad4ffc1
  rag:
    retrieval-model: multiple
    retrieval-top-k: 4
    retrieval-score-threshold: 0.3
    reranking-mode: reranking_model  # or weighted_score
    reranking-model:
      name:
      provider:
    weighted_score:
      keyword_weight:
      vector_weight:

# llm
llm:
  model:
    debug: false
  # 对话
  conversation:
    # 命名模型
    naming-model:
    # 多模态模型
    multimodal-model:
    # RAG文件
    document:
      accept: ".txt,.markdown,.md,.mdx,.pdf,.html,.htm,.xlsx,.xls,.docx,.csv,.TXT,.MARKDOWN,.MD,.MDX,.PDF,.HTML,.HTM,.XLSX,.XLS,.DOCX,.CSV"
    # 网络搜索
    search:
      count: 10
  # 记忆
  memory:
    # 最大容量
    max-size: 50
    # 缓存
    redis:
      key-prefix: "dubhe:llm:memory:"

# 微信公众平台
wx:
  # 企业微信
  cp:
    enabled: false
    corp-id:
    corp-secret:
    redirect:
  # 小程序
  ma:
    enabled: true
    app-id:
    secret:
    msg-data-format: JSON
    version: develop
  # 粤政易
  yzy:
    enabled: false
    base-url: https://zwwx.gdzwfw.gov.cn
    corp-id:
    corp-secret:
    redirect: