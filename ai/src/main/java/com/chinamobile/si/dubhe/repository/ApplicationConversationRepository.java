package com.chinamobile.si.dubhe.repository;

import com.agentsflex.core.llm.BaseLlm;
import com.agentsflex.core.llm.ChatContext;
import com.agentsflex.core.llm.LlmConfig;
import com.agentsflex.core.llm.StreamResponseListener;
import com.agentsflex.core.llm.response.AiMessageResponse;
import com.agentsflex.core.message.HumanMessage;
import com.agentsflex.core.message.MessageStatus;
import com.agentsflex.core.message.SystemMessage;
import com.agentsflex.core.prompt.HistoriesPrompt;
import com.agentsflex.core.prompt.ImagePrompt;
import com.alibaba.fastjson.JSONPath;
import com.chinamobile.si.dubhe.model.*;
import com.chinamobile.sparrow.ai.infra.llm.plugin.IPlugin;
import com.chinamobile.sparrow.ai.model.llm.Dataset;
import com.chinamobile.sparrow.ai.model.llm.DatasetDocument;
import com.chinamobile.sparrow.ai.model.llm.Memory;
import com.chinamobile.sparrow.ai.model.llm.Model;
import com.chinamobile.sparrow.ai.repository.llm.ConversationRepository;
import com.chinamobile.sparrow.ai.repository.llm.DatasetRepository;
import com.chinamobile.sparrow.ai.repository.llm.MemoryRepository;
import com.chinamobile.sparrow.ai.repository.llm.ModelRepository;
import com.chinamobile.sparrow.ai.service.dify.ConsoleFacade;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.repository.media.AbstractMediaRepository;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.google.common.reflect.TypeToken;
import com.google.gson.JsonObject;
import okhttp3.Response;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import org.jetbrains.annotations.NotNull;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.tuples.Tuple3;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.persistence.EntityManagerFactory;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Repository
public class ApplicationConversationRepository extends ConversationRepository<ApplicationConversation> {

    final String difyAppId;
    final DefaultApplicationRepository applicationRepository;

    public ApplicationConversationRepository(
            @Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
            @Value(value = "${dify.general-chat-app-id}") String difyAppId,
            @Value(value = "${llm.conversation.multimodal-model}") String multimodalModel,
            @Value(value = "${llm.conversation.naming-model}") String namingModel,
            @Value(value = "${llm.memory.max-size}") Integer memorySize,
            @Value(value = "${dify.rag.retrieval-model}") String retrievalModel,
            @Value(value = "${dify.rag.retrieval-top-k}") Integer retrievalTopK,
            @Value(value = "${dify.rag.retrieval-score-threshold}") Double retrievalScore,
            @Value(value = "${dify.rag.reranking-model.name}") String rerankingModelName,
            @Value(value = "${dify.rag.reranking-model.provider}") String rerankingProviderName,
            ModelRepository<? extends Model> modelRepository,
            MemoryRepository<? extends Memory> memoryRepository,
            DatasetRepository<? extends Dataset> datasetRepository,
            DefaultApplicationRepository applicationRepository,
            AbstractMediaRepository mediaRepository,
            List<IPlugin> pluginImpls,
            ConsoleFacade consoleFacade,
            ApplicationEventPublisher eventPublisher
    ) {
        super(entityManagerFactory, jinqJPAStreamProvider, ApplicationConversation.class, multimodalModel, namingModel, memorySize, retrievalModel, retrievalTopK, retrievalScore, rerankingModelName, rerankingProviderName, null, null, modelRepository, memoryRepository, datasetRepository, mediaRepository, pluginImpls, consoleFacade, eventPublisher);
        this.difyAppId = difyAppId;
        this.applicationRepository = applicationRepository;
    }

    @Override
    @Deprecated
    public Result<String> addOrDefault(String originId, String operatorId) {
        throw new UnsupportedOperationException();
    }

    public Result<String> addOrDefault(String applicationId, String originId, String operatorId) {
        Result<String> _id = new Result<>();

        if (StringUtils.hasLength(originId) && memoryRepository.find(1, originId).isEmpty()) {
            _id.data = originId;
            return _id;
        }

        HistoriesPrompt _prompt = memoryRepository.prompt(null);

        ApplicationConversation _record = new ApplicationConversation(applicationId);
        _record.setId(_prompt.getMemory().id().toString());
        super.add(_record, operatorId);

        _id.data = _record.getId();
        return _id;
    }

    @Override
    public SseEmitter generate(String id, String model, List<String> plugins, Integer memorySize, String mediaId, String message, String operatorId) {
        SseEmitter _sse = new SseEmitter(-1L);

        Result<ApplicationConversation> _record = ((ApplicationConversationRepository) AopContext.currentProxy()).permit(id, operatorId);
        if (!_record.isOK()) {
            _sse.completeWithError(new IllegalAccessException(_record.message));
            return _sse;
        }

        if (!StringUtils.hasLength(_record.data.getApplicationId())) {
            return generate(id, model, plugins, memorySize, mediaId, message, operatorId);
        }

        Result<DefaultApplication> _application = applicationRepository.get(_record.data.getApplicationId());
        if (!_application.isOK()) {
            _sse.completeWithError(new IllegalAccessException(_application.message));
            return _sse;
        }

        if (_application.data instanceof LinkApplication || _application.data instanceof WorkflowApplication) {
            _sse.completeWithError(new IllegalAccessException(_application.data.getName()));
            return _sse;
        }

        if (_application.data instanceof DatasetApplication) {
            String _temp = ((DatasetApplication) _application.data).getPrompt();
            List<String> _datasetIds = ((DatasetApplication) _application.data).getDatasets();

            return generate((params, sse) -> {
                String _conversationId = params.getDifyId();

                String _provider = (String) modelRepository.languageModels().stream()
                        .filter(i -> Objects.equals(((Tuple3<String, String, BaseLlm<? extends LlmConfig>>) i).getOne(), model))
                        .map(i -> ((Tuple3<String, String, BaseLlm<? extends LlmConfig>>) i).getTwo())
                        .findFirst().orElse(null);

                final Date[] _start = new Date[]{null};
                final Long[] _duration = new Long[]{null};

                EventSourceListener _listener = new EventSourceListener() {

                    public void onOpen(@NotNull EventSource eventSource, @NotNull Response response) {
                        if (!params.getLinks().isEmpty()) {
                            try {
                                ResponseMessage _message = new ResponseMessage();
                                _message.setLinks(params.getLinks());
                                sse.send(_message);
                            } catch (IOException e) {
                                sse.completeWithError(e);
                            }
                        }

                    }

                    public void onEvent(@NotNull EventSource eventSource, String eventId, String type, @NotNull String data) {
                        if (!StringUtils.hasLength(_conversationId)) {
                            String _id = JSONPath.read(data, "$.conversation_id", String.class);
                            eventPublisher.publishEvent(new UpdateConversationIdEvent(id, _id, operatorId));
                        }

                        JsonObject _json = ConverterUtil.json2Object(data, JsonObject.class);

                        ResponseMessage _message = new ResponseMessage();

                        switch ((String) JSONPath.read(data, "$.event", String.class)) {
                            case "message":
                                String _text = _json.get("answer").getAsString();
                                if (_start[0] == null && StringUtils.trimWhitespace(_text).equals("<think>")) {
                                    _start[0] = new Date();
                                } else {
                                    if (_start[0] != null && _duration[0] == null && StringUtils.trimWhitespace(_text).equals("</think>")) {
                                        _duration[0] = new Date().getTime() - _start[0].getTime();

                                        Memory.Reasoning _reasoning = new Memory.Reasoning();
                                        _reasoning.setDuration(_duration[0]);
                                        _message.setReasoning(_reasoning);
                                    } else if (_start[0] != null && _duration[0] == null) {
                                        Memory.Reasoning _reasoning = new Memory.Reasoning();
                                        _reasoning.setDescription(_text);
                                        _message.setReasoning(_reasoning);
                                    } else {
                                        _message.setText(_text);
                                    }

                                    try {
                                        sse.send(_message);
                                    } catch (IOException e) {
                                        sse.completeWithError(e);
                                    }
                                }

                                break;
                            case "message_end":
                                if (JSONPath.contains(data, "$.metadata.retriever_resources")) {
                                    String _temp = _json.get("metadata").getAsJsonObject().get("retriever_resources").toString();
                                    List<JsonObject> _resources = ConverterUtil.json2Object(_temp, (new TypeToken<List<JsonObject>>() {
                                    }).getType());

                                    List<Segment> _segments = _resources.stream()
                                            .map(i -> {
                                                Segment _segment = new Segment();
                                                _segment.setId(i.get("document_id").getAsString());
                                                _segment.setName(i.get("document_name").getAsString());
                                                _segment.setContent(i.get("content").getAsString());
                                                _segment.setScore(i.get("score").getAsDouble());

                                                return _segment;
                                            })
                                            .collect(Collectors.toList());

                                    List<String> _documentIds = _segments.stream()
                                            .map(Document::getId)
                                            .distinct()
                                            .collect(Collectors.toList());

                                    List<DatasetDocument> _medias = datasetRepository.findDocuments(_documentIds, false);
                                    _segments = _medias.stream()
                                            .map(i -> {
                                                Segment _segment = new Segment();
                                                _segment.setId(i.getId());
                                                _segment.setName(i.getDocument().getName());
                                                return _segment;
                                            })
                                            .collect(Collectors.toList());
                                    if (!_segments.isEmpty()) {
                                        _message.setSegments(_segments);

                                        try {
                                            sse.send(_message);
                                        } catch (IOException e) {
                                            sse.completeWithError(e);
                                        }
                                    }
                                }

                                break;
                            case "error":
                                String _error = JSONPath.read(data, "$.message", String.class);
                                sse.completeWithError(new Exception(_error));

                                break;
                        }

                    }

                    public void onClosed(@NotNull EventSource eventSource) {
                        sse.complete();
                    }

                    public void onFailure(@NotNull EventSource eventSource, Throwable e, Response response) {
                        sse.completeWithError(e);
                    }
                };

                String _systemPrompt = _temp;
                if (StringUtils.hasLength(params.getPrompt())) {
                    _systemPrompt += "\n" + params.getPrompt();
                }

                if (StringUtils.hasLength(rerankingModelName)) {
                    consoleFacade.chat(difyAppId, _conversationId, model, _provider, _systemPrompt, _datasetIds, retrievalModel, retrievalTopK, retrievalScore, rerankingModelName, rerankingProviderName, message, _listener);
                } else {
                    consoleFacade.chat(difyAppId, _conversationId, model, _provider, _systemPrompt, _datasetIds, retrievalModel, retrievalTopK, retrievalScore, keywordWeight, vectorWeight, message, _listener);
                }
            }, id, plugins, message, operatorId);
        } else {
            String _temp = ((PromptApplication) _application.data).getPrompt();

            return generate((params, sse) -> {
                HistoriesPrompt _prompt = memoryRepository.prompt(id);

                int _memorySize = memorySize == null ? memoryMaxSize : memorySize;
                _prompt.setMaxAttachedMessageCount(_memorySize % 2 == 0 ? _memorySize + 1 : _memorySize);

                String _model = !StringUtils.hasLength(mediaId) && _prompt.getMemory().getMessages().stream().noneMatch(i -> i instanceof ImagePrompt.TextAndImageMessage)
                        ? model
                        : multimodalModel;
                BaseLlm<? extends LlmConfig> _languageModel = (BaseLlm<? extends LlmConfig>) modelRepository.languageModels().stream()
                        .filter(i -> Objects.equals(((Tuple3<String, String, BaseLlm<? extends LlmConfig>>) i).getOne(), _model))
                        .findFirst().orElse(null);
                if (_languageModel == null) {
                    sse.completeWithError(new IllegalArgumentException());
                    return;
                }

                String _systemPrompt = _temp;
                if (StringUtils.hasLength(params.getPrompt())) {
                    _systemPrompt += "\n" + params.getPrompt();
                }
                _prompt.setSystemMessage(new SystemMessage(_systemPrompt));

                HumanMessage _message;
                if (StringUtils.hasLength(mediaId)) {
                    _message = new ImagePrompt.TextAndImageMessage(new ImagePrompt(message, mediaId));
                    _message.addMetadata(MESSAGE_METADATA_MEDIA_ID, mediaId);
                    _prompt.addMessage(_message);
                } else {
                    _message = new HumanMessage(message);
                }

                _message.addMetadata(MESSAGE_METADATA_ID, memoryRepository.id());
                _message.addMetadata(MESSAGE_METADATA_USER, operatorId);
                _message.addMetadata(MESSAGE_METADATA_TIMESTAMP, new Date());
                _prompt.addMessage(_message);

                final Boolean[] _reasoningField = new Boolean[]{null};
                final String[] _reasoning = new String[]{""};
                final Date[] _start = new Date[]{null};
                final Long[] _duration = new Long[]{null};

                _languageModel.chatStream(_prompt, new StreamResponseListener() {
                    public void onStart(ChatContext context) {
                        if (!params.getLinks().isEmpty()) {
                            try {
                                ResponseMessage _message = new ResponseMessage();
                                _message.setLinks(params.getLinks());
                                sse.send(_message);
                            } catch (IOException e) {
                                sse.completeWithError(e);
                            }
                        }

                    }

                    public void onMessage(ChatContext context, AiMessageResponse message) {
                        try {
                            ResponseMessage _message = new ResponseMessage();

                            if (StringUtils.hasLength(message.getMessage().getReasoningContent())) {
                                if (_reasoningField[0] == null) {
                                    _reasoningField[0] = true;
                                }

                                if (_start[0] == null) {
                                    _start[0] = new Date();
                                }

                                _reasoning[0] += message.getMessage().getReasoningContent();

                                Memory.Reasoning _reasoning = new Memory.Reasoning();
                                _reasoning.setDescription(message.getMessage().getReasoningContent());
                                _message.setReasoning(_reasoning);
                                sse.send(_message);
                            } else {
                                if (_reasoningField[0] == null) {
                                    _reasoningField[0] = false;
                                }

                                if (_reasoningField[0] && _duration[0] == null) {
                                    _duration[0] = new Date().getTime() - _start[0].getTime();

                                    Memory.Reasoning _reasoning = new Memory.Reasoning();
                                    _reasoning.setDuration(_duration[0]);
                                    _message.setReasoning(_reasoning);
                                    sse.send(_message);
                                } else {
                                    String _text = message.getMessage().getContent();
                                    if (StringUtils.hasLength(_text)) {
                                        String _fullText = message.getMessage().getMessageContent().toString();

                                        // 开始思考
                                        if (_start[0] == null && StringUtils.trimWhitespace(_text).equals("<think>") && _text.equals(_fullText)) {
                                            _start[0] = new Date();
                                        }
                                        // 思考中
                                        else if (_start[0] != null && _duration[0] == null) {
                                            // 结束思考
                                            if (StringUtils.trimWhitespace(_text).equals("</think>")) {
                                                _duration[0] = (new Date()).getTime() - _start[0].getTime();

                                                Memory.Reasoning _reasoning = new Memory.Reasoning();
                                                _reasoning.setDuration(_duration[0]);
                                                _message.setReasoning(_reasoning);
                                                sse.send(_message);
                                            }
                                            // 思考中
                                            else {
                                                _reasoning[0] += _text;

                                                Memory.Reasoning _reasoning = new Memory.Reasoning();
                                                _reasoning.setDescription(_text);
                                                _message.setReasoning(_reasoning);
                                                sse.send(_message);
                                            }
                                        } else {
                                            _message.setText(_text);
                                            sse.send(_message);
                                        }
                                    }
                                }
                            }

                            if (MessageStatus.END.equals(message.getMessage().getStatus())) {
                                if (StringUtils.hasLength(_reasoning[0])) {
                                    message.getMessage().addMetadata(MESSAGE_METADATA_REASONING, _reasoning[0]);
                                    message.getMessage().addMetadata(MESSAGE_METADATA_REASONING_DURATION, _duration[0]);
                                }

                                message.getMessage().addMetadata(MESSAGE_METADATA_ID, memoryRepository.id());
                                message.getMessage().addMetadata(MESSAGE_METADATA_TIMESTAMP, new Date());
                            }
                        } catch (IOException e) {
                            sse.completeWithError(e);
                        }

                    }

                    public void onStop(ChatContext context) {
                        sse.complete();
                    }

                    public void onFailure(ChatContext context, Throwable throwable) {
                        sse.completeWithError(throwable);
                    }
                });
            }, id, plugins, message, operatorId);
        }
    }

}