package com.chinamobile.si.dubhe.model;

import com.chinamobile.sparrow.ai.model.llm.Application;

import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;

/**
 * 工作流应用
 */
@Entity
@DiscriminatorValue(value = "Workflow")
public class WorkflowApplication extends DefaultApplication {

    public WorkflowApplication() {
    }

    public WorkflowApplication(Application.ENUM_PLATFORM platform, String platformId) {
        this.setPlatform(platform);
        this.setPlatformId(platformId);
    }

}