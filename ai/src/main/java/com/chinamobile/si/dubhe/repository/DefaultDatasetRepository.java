package com.chinamobile.si.dubhe.repository;

import com.chinamobile.si.dubhe.model.DatasetScope;
import com.chinamobile.si.dubhe.model.DefaultDataset;
import com.chinamobile.sparrow.ai.model.llm.Dataset;
import com.chinamobile.sparrow.ai.repository.llm.DatasetRepository;
import com.chinamobile.sparrow.ai.service.dify.DatasetFacade;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.PaginatedRecords;
import com.chinamobile.sparrow.domain.lang.ResultWarpperRuntimeException;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.media.AbstractMediaRepository;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.jinq.tuples.Pair;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Repository
public class DefaultDatasetRepository extends DatasetRepository<DefaultDataset> {

    public DefaultDatasetRepository(
            @Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
            @Value(value = "${spring.application.name}") String applicationName,
            AbstractMediaRepository mediaRepository,
            DatasetFacade datasetFacade
    ) {
        super(entityManagerFactory, jinqJPAStreamProvider, DefaultDataset.class, applicationName, mediaRepository, datasetFacade);
    }

    @Override
    @Transactional
    public Result<Void> remove(String id, String operatorId) throws IOException {
        Result<Void> _success = new Result<>();

        Result<DefaultDataset> _record = ((DefaultDatasetRepository) AopContext.currentProxy()).permit(id, operatorId);
        if (!_record.isOK()) {
            return _success.pack(_record);
        }

        // 删除授权
        getCurrentSession().createQuery("delete from DatasetScope where datasetId = :id")
                .setParameter("id", id)
                .executeUpdate();
        getCurrentSession().remove(_record.data);

        // 删除知识库
        getCurrentSession().remove(_record.data);

        _success = datasetFacade.delete(_record.data.getDifyId());
        if(!_success.isOK()) {
            throw new ResultWarpperRuntimeException(_success);
        }

        return _success;
    }

    public PaginatedRecords<DefaultDataset> search(int count, int index, String title, String userId) {
        PaginatedRecords<DefaultDataset> _page = super.search(count, index, title);
        parseOwner(_page.records, userId);
        return _page;
    }

    @Transactional(readOnly = true)
    public List<DefaultDataset> searchUserDatasets(String title, User user) {
        String _userId = user.getId();

        List<String> _deptIds = StringUtils.hasLength(user.getDeptCode())
                ? Arrays.asList(user.getDeptCode().split("\\" + Department.CODE_SEPARATOR))
                : new ArrayList<>();

        DatasetScope.ENUM_TYPE _deptScope = DatasetScope.ENUM_TYPE.DEPARTMENT;
        DatasetScope.ENUM_TYPE _userScope = DatasetScope.ENUM_TYPE.USER;
        JinqStream<DefaultDataset> _query = stream(DefaultDataset.class).leftOuterJoin((i, source) -> source.stream(DatasetScope.class), (i, j) -> j.getDatasetId().equals(i.getId()))
                .where(i -> _userId.equals(i.getOne().getCreatorId()) || (i.getTwo() != null && (
                                (_deptScope == i.getTwo().getType() && _deptIds.contains(i.getTwo().getTargetId())) || (_userScope == i.getTwo().getType() && _userId.equals(i.getTwo().getTargetId()))
                        ))
                )
                .select(Pair::getOne);

        if (StringUtils.hasLength(title)) {
            _query = _query.where(i -> i.getTitle().contains(title));
        }

        List<DefaultDataset> _records = _query.toList();
        parseOwner(_records, user.getId());
        return _records;
    }

    public Result<String> addScope(String id, DatasetScope.ENUM_TYPE type, String targetId, String operatorId) {
        Result<String> _id = new Result<>();

        Result<DefaultDataset> _record = ((DatasetRepository<DefaultDataset>) AopContext.currentProxy()).get(id);
        if (!_record.isOK()) {
            return _id.pack(_record);
        }

        DatasetScope _scope = stream(DatasetScope.class)
                .where(i -> i.getDatasetId().equals(id) && i.getType() == type && i.getTargetId().equals(targetId))
                .findFirst().orElse(null);
        if (_scope == null) {
            _scope = new DatasetScope();
            _scope.setDatasetId(id);
            _scope.setType(type);
            _scope.setTargetId(targetId);
            _scope.setCreatorId(operatorId);
            _scope.setCreateTime(new Date());

            getCurrentSession().save(_scope);
        }

        _id.data = _scope.getId();
        return _id;
    }

    @Transactional(readOnly = true)
    public PaginatedRecords<DatasetScope> searchScopes(int count, int index, String id, String name) {
        DatasetScope.ENUM_TYPE _deptScope = DatasetScope.ENUM_TYPE.DEPARTMENT;
        DatasetScope.ENUM_TYPE _userScope = DatasetScope.ENUM_TYPE.USER;

        JinqStream<Pair<Pair<DatasetScope, Department>, User>> _query = stream(DatasetScope.class).where(i -> id.equals(i.getDatasetId()))
                .leftOuterJoin((i, source) -> source.stream(Department.class), (i, j) -> _deptScope == i.getType() && j.getId().equals(i.getTargetId()))
                .leftOuterJoin((i, source) -> source.stream(User.class), (i, j) -> _userScope == i.getOne().getType() && j.getId().equals(i.getOne().getTargetId()));

        _query = StringUtils.hasLength(name)
                ? _query.where(i -> (_deptScope == i.getOne().getOne().getType() && i.getOne().getTwo().getName().contains(name)) || (_userScope == i.getOne().getOne().getType() && i.getTwo().getName().contains(name)))
                : _query.where(i -> i.getOne().getTwo() != null || i.getTwo() != null);

        PaginatedRecords<DatasetScope> _page = new PaginatedRecords<>(count, index);
        _page.total = _query.count();

        if (count >= 0 && index >= 0) {
            _query = _query.skip((long) count * index).limit(count);
        }

        _page.records = _query.toList().stream()
                .map(i -> {
                    DatasetScope _scope = i.getOne().getOne();
                    _scope.setName(i.getTwo() == null ? i.getOne().getTwo().getName() : i.getTwo().getName());
                    return _scope;
                })
                .collect(Collectors.toList());
        return _page;
    }

    public Result<Void> removeScope(String scopeId) {
        Result<Void> _success = new Result<>();

        DatasetScope _scope = getCurrentSession().get(DatasetScope.class, scopeId);
        if (_scope == null) {
            _success.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{DatasetScope.class.getSimpleName()});
            return _success;
        }

        getCurrentSession().remove(_scope);

        return _success;
    }

    void parseOwner(List<DefaultDataset> records, String userId) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }

        records.forEach(i -> {
            i.setIsOwner(Objects.equals(i.getCreatorId(), userId));
        });
    }

}