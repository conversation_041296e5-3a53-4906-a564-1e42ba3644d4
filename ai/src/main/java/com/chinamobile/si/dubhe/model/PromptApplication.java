package com.chinamobile.si.dubhe.model;

import org.springframework.util.StringUtils;

import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;

/**
 * 提示词应用
 */
@Entity
@DiscriminatorValue(value = "Prompt")
public class PromptApplication extends DefaultApplication {

    String prompt;

    String fieldsKey;

    public String getPrompt() {
        return prompt;
    }

    public void setPrompt(String prompt) {
        this.prompt = StringUtils.trimWhitespace(prompt);
    }

    public String getFieldsKey() {
        return fieldsKey;
    }

    public void setFieldsKey(String fieldsKey) {
        this.fieldsKey = StringUtils.trimWhitespace(fieldsKey);
    }

}