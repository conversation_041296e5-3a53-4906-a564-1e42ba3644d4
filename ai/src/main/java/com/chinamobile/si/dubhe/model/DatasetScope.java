package com.chinamobile.si.dubhe.model;

import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.util.IdWorker;

import javax.persistence.*;

@Entity
@Table(name = "llm_dataset_scope")
public class DatasetScope extends AbstractEntity {

    @Id
    @Column(length = 36)
    String id = String.valueOf(IdWorker.getInstance().nextId());

    @Column(length = 36)
    String datasetId;

    ENUM_TYPE type;

    String targetId;

    @Transient
    String name;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDatasetId() {
        return datasetId;
    }

    public void setDatasetId(String datasetId) {
        this.datasetId = datasetId;
    }

    public ENUM_TYPE getType() {
        return type;
    }

    public void setType(ENUM_TYPE type) {
        this.type = type;
    }

    public String getTargetId() {
        return targetId;
    }

    public void setTargetId(String readerId) {
        this.targetId = readerId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static enum ENUM_TYPE {
        DEPARTMENT, USER;
    }

}