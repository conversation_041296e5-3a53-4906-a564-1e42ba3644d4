package com.chinamobile.si.dubhe.model;

import org.springframework.util.StringUtils;

import javax.persistence.Column;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import javax.persistence.Transient;
import java.util.List;

/**
 * 知识库应用
 */
@Entity
@DiscriminatorValue(value = "Dataset")
public class DatasetApplication extends DefaultApplication {

    String datasetsKey;

    @Transient
    List<String> datasets;

    @Column(columnDefinition = "text")
    String prompt;

    public String getDatasetsKey() {
        return datasetsKey;
    }

    public void setDatasetsKey(String datasetKeys) {
        this.datasetsKey = StringUtils.trimWhitespace(datasetKeys);
    }

    public List<String> getDatasets() {
        return this.datasets;
    }

    public void setDatasets(List<String> datasets) {
        this.datasets = datasets;
    }

    public String getPrompt() {
        return prompt;
    }

    public void setPrompt(String prompt) {
        this.prompt = StringUtils.trimWhitespace(prompt);
    }

}