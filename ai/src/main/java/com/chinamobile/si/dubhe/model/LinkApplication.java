package com.chinamobile.si.dubhe.model;

import org.springframework.util.StringUtils;

import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;

/**
 * 外链式应用
 */
@Entity
@DiscriminatorValue(value = "Link")
public class LinkApplication extends DefaultApplication {

    String link;

    ENUM_TARGET target;

    public String getLink() {
        return link;
    }

    public void setLink(String link) {
        this.link = StringUtils.trimWhitespace(link);
    }

    public ENUM_TARGET getTarget() {
        return target;
    }

    public void setTarget(ENUM_TARGET target) {
        this.target = target;
    }

    public static enum ENUM_TARGET {
        BLANK, SELF;
    }

}