package com.chinamobile.si.dubhe.model;

import com.chinamobile.sparrow.ai.model.llm.Application;

import javax.persistence.Entity;
import javax.persistence.Transient;

@Entity
public class DefaultApplication extends Application {

    @Transient
    boolean isOwner;

    public boolean getIsOwner() {
        return isOwner;
    }

    public void setIsOwner(boolean owner) {
        isOwner = owner;
    }

}