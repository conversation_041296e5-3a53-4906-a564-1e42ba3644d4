package com.chinamobile.si.dubhe.model;

import com.chinamobile.sparrow.ai.model.llm.Conversation;

import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;

@Entity
@DiscriminatorValue(value = "Application")
public class ApplicationConversation extends Conversation {

    String applicationId;

    public ApplicationConversation() {
    }

    public ApplicationConversation(String applicationId) {
        this.applicationId = applicationId;
    }

    public String getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(String applicationId) {
        this.applicationId = applicationId;
    }

}