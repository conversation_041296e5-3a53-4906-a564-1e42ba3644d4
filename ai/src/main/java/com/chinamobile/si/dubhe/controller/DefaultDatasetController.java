package com.chinamobile.si.dubhe.controller;

import com.chinamobile.si.dubhe.model.DatasetScope;
import com.chinamobile.si.dubhe.model.DefaultDataset;
import com.chinamobile.si.dubhe.repository.DefaultDatasetRepository;
import com.chinamobile.sparrow.ai.model.llm.Dataset;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.lang.PaginatedRecords;
import com.chinamobile.sparrow.springboot.web.controller.ai.DatasetController;
import com.google.gson.JsonObject;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping(value = "llm/dataset")
public class DefaultDatasetController extends DatasetController {

    public DefaultDatasetController(
            @Value(value = "${llm.conversation.document.accept}") String accept,
            DefaultDatasetRepository datasetRepository,
            LoginUtil loginUtil
    ) {
        super(accept, datasetRepository, loginUtil);
    }

    @Override
    public Result<Dataset> get(@RequestBody JsonObject data) {
        return super.get(data);
    }

    @Override
    @Deprecated
    public Result<String> save(@RequestBody Dataset record) {
        throw new UnsupportedOperationException();
    }

    @PostMapping(value = "/save/default")
    public Result<String> save(@RequestBody DefaultDataset record) throws InstantiationException, IllegalAccessException {
        return ((DefaultDatasetRepository) datasetRepository).save(record, loginUtil.getUser());
    }

    @Override
    public Result<Void> remove(@RequestBody JsonObject data) throws IOException {
        return super.remove(data);
    }

    @PostMapping(value = "/me")
    @ResponseBody
    public Result<List<DefaultDataset>> me() {
        Result<List<DefaultDataset>> _records = new Result<>();
        _records.data = ((DefaultDatasetRepository) datasetRepository).me(loginUtil.getUserId());
        return _records;
    }

    @PostMapping(value = "/search/scope")
    @ResponseBody
    public Result<List<DefaultDataset>> searchUserApplications(@RequestBody JsonObject data) {
        String _title = Optional.ofNullable(data.get("title"))
                .map((i) -> i.isJsonNull() ? null : i.getAsString())
                .orElse(null);

        Result<List<DefaultDataset>> _records = new Result<>();
        _records.data = ((DefaultDatasetRepository) datasetRepository).searchUserDatasets(_title, loginUtil.getUser());
        return _records;
    }

    @PostMapping(value = "/scope/add")
    @ResponseBody
    @RequiresPermissions(value = "llm:dataset:scope:add")
    public Result<String> addScope(@RequestBody JsonObject data) {
        String _datasetId = data.get("datasetId").getAsString();
        DatasetScope.ENUM_TYPE _type = DatasetScope.ENUM_TYPE.valueOf(data.get("type").getAsString());
        String _targetId = data.get("targetId").getAsString();

        return ((DefaultDatasetRepository) datasetRepository).addScope(_datasetId, _type, _targetId, loginUtil.getUserId());
    }

    @PostMapping(value = "/scope/search")
    @ResponseBody
    @RequiresPermissions(value = "llm:dataset:scope:search")
    public Result<PaginatedRecords<DatasetScope>> searchScopes(@RequestBody JsonObject data) {
        int _count = data.get("count").getAsInt();
        int _index = data.get("index").getAsInt();
        String _datasetId = data.get("datasetId").getAsString();
        String _name = Optional.ofNullable(data.get("name"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);

        Result<PaginatedRecords<DatasetScope>> _page = new Result<>();
        _page.data = ((DefaultDatasetRepository) datasetRepository).searchScopes(_count, _index, _datasetId, _name);
        return _page;
    }

    @PostMapping(value = "/scope/remove")
    @ResponseBody
    @RequiresPermissions(value = "llm:dataset:scope:remove")
    public Result<Void> removeScope(@RequestBody JsonObject data) {
        String _scopeId = data.get("scopeId").getAsString();

        return ((DefaultDatasetRepository) datasetRepository).removeScope(_scopeId);
    }

}