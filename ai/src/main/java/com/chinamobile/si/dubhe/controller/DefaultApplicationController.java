package com.chinamobile.si.dubhe.controller;

import com.chinamobile.si.dubhe.model.*;
import com.chinamobile.si.dubhe.repository.DefaultApplicationRepository;
import com.chinamobile.sparrow.ai.model.llm.Application;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.lang.PaginatedRecords;
import com.chinamobile.sparrow.springboot.web.controller.ai.ApplicationController;
import com.google.gson.JsonObject;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping(value = "llm/application")
public class DefaultApplicationController extends ApplicationController {

    public DefaultApplicationController(
            DefaultApplicationRepository applicationRepository,
            LoginUtil loginUtil
    ) {
        super(applicationRepository, loginUtil);
    }

    @Deprecated
    @Override
    public Result<PaginatedRecords<Application>> search(JsonObject data) {
        throw new UnsupportedOperationException();
    }

    @Deprecated
    @Override
    public Result<String> save(Application record) {
        throw new UnsupportedOperationException();
    }

    @PostMapping(value = "/save/dataset")
    public Result<String> save(@RequestBody DatasetApplication record) {
        return ((DefaultApplicationRepository) applicationRepository).save(record, loginUtil.getUserId());
    }

    @PostMapping(value = "/save/link")
    public Result<String> save(@RequestBody LinkApplication record) {
        return ((DefaultApplicationRepository) applicationRepository).save(record, loginUtil.getUserId());
    }

    @PostMapping(value = "/save/prompt")
    public Result<String> save(@RequestBody PromptApplication record) {
        return ((DefaultApplicationRepository) applicationRepository).save(record, loginUtil.getUserId());
    }

    @PostMapping(value = "/save/workflow")
    public Result<String> save(@RequestBody WorkflowApplication record) {
        return ((DefaultApplicationRepository) applicationRepository).save(record, loginUtil.getUserId());
    }

    @PostMapping(value = "/search/default")
    @RequiresPermissions(value = "llm:application:search")
    public Result<PaginatedRecords<DefaultApplication>> searchDefault(@RequestBody JsonObject data) {
        int _count = data.get("count").getAsInt();
        int _index = data.get("index").getAsInt();
        String _type = Optional.ofNullable(data.get("type"))
                .map(i -> i.isJsonNull() ? "" : i.getAsString()).orElse("");
        String _name = Optional.ofNullable(data.get("name"))
                .map((i) -> i.isJsonNull() ? null : i.getAsString()).orElse(null);

        Result<PaginatedRecords<DefaultApplication>> _page = new Result<>();
        _page.data = ((DefaultApplicationRepository) applicationRepository).search(_count, _index, _type, _name);
        applicationRepository.parseUsers(_page.data.records);
        return _page;
    }

    @PostMapping(value = "/search/scope")
    public Result<List<DefaultApplication>> searchUserApplications(@RequestBody JsonObject data) {
        String _type = Optional.ofNullable(data.get("type"))
                .map(i -> i.isJsonNull() ? "" : i.getAsString()).orElse("");
        String _name = Optional.ofNullable(data.get("name"))
                .map((i) -> i.isJsonNull() ? null : i.getAsString()).orElse(null);

        Result<List<DefaultApplication>> _records = new Result<>();
        _records.data = ((DefaultApplicationRepository) applicationRepository).searchUserApplications(_type, _name, loginUtil.getUser());
        return _records;
    }

    @PostMapping(value = "/scope/add")
    @ResponseBody
    @RequiresPermissions(value = "llm:application:scope:add")
    public Result<String> addScope(@RequestBody JsonObject data) {
        String _applicationId = data.get("applicationId").getAsString();
        ApplicationScope.ENUM_TYPE _type = ApplicationScope.ENUM_TYPE.valueOf(data.get("type").getAsString());
        String _targetId = data.get("targetId").getAsString();

        return ((DefaultApplicationRepository) applicationRepository).addScope(_applicationId, _type, _targetId, loginUtil.getUserId());
    }

    @PostMapping(value = "/scope/search")
    @ResponseBody
    @RequiresPermissions(value = "llm:application:scope:search")
    public Result<PaginatedRecords<ApplicationScope>> searchScopes(@RequestBody JsonObject data) {
        int _count = data.get("count").getAsInt();
        int _index = data.get("index").getAsInt();
        String _applicationId = data.get("applicationId").getAsString();
        String _type = Optional.ofNullable(data.get("type"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _name = Optional.ofNullable(data.get("name"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);

        Result<PaginatedRecords<ApplicationScope>> _page = new Result<>();
        _page.data = ((DefaultApplicationRepository) applicationRepository).searchScopes(_count, _index, _applicationId, _type, _name);
        return _page;
    }

    @PostMapping(value = "/scope/remove")
    @ResponseBody
    @RequiresPermissions(value = "llm:application:scope:remove")
    public Result<Void> removeScope(@RequestBody JsonObject data) {
        String _scopeId = data.get("scopeId").getAsString();

        return ((DefaultApplicationRepository) applicationRepository).removeScope(_scopeId);
    }

}