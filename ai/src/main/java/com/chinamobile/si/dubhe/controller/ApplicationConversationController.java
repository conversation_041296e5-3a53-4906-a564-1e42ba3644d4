package com.chinamobile.si.dubhe.controller;

import com.chinamobile.si.dubhe.repository.ApplicationConversationRepository;
import com.chinamobile.sparrow.ai.repository.llm.MemoryRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.chinamobile.sparrow.springboot.web.controller.ai.ConversationController;
import com.google.common.reflect.TypeToken;
import com.google.gson.JsonObject;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping(value = "llm/conversation")
public class ApplicationConversationController extends ConversationController {

    public ApplicationConversationController(
            ApplicationConversationRepository conversationRepository,
            MemoryRepository memoryRepository,
            LoginUtil loginUtil
    ) {
        super(conversationRepository, memoryRepository, loginUtil);
    }

    @Override
    public Result<String> newConversation(@RequestBody(required = false) JsonObject data) {
        String _applicationId = Optional.ofNullable(data.get("applicationId"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _originId = Optional.ofNullable(data.get("originId"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);

        return ((ApplicationConversationRepository) conversationRepository).addOrDefault(_applicationId, _originId, loginUtil.getUserId());
    }

    @Override
    public SseEmitter chat(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();
        String _model = data.get("model").getAsString();
        List<String> _plugins = (List<String>) Optional.ofNullable(data.get("plugins"))
                .map(i -> i.isJsonNull() ? null : ConverterUtil.json2Object(i.toString(), new TypeToken<List<String>>() {
                }.getType())).orElse(null);
        Integer _memorySize = Optional.ofNullable(data.get("memorySize"))
                .map(i -> i.isJsonNull() ? null : i.getAsInt()).orElse(null);
        String _message = data.get("message").getAsString();
        String _mediaId = Optional.ofNullable(data.get("mediaId"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);

        return conversationRepository.generate(_id, _model, _plugins, _memorySize, _message, _mediaId, loginUtil.getUserId());
    }

}