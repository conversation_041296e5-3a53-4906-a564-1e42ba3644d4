package com.chinamobile.si.dubhe.repository;

import com.chinamobile.si.dubhe.model.*;
import com.chinamobile.sparrow.ai.model.llm.Application;
import com.chinamobile.sparrow.ai.repository.llm.ApplicationRepository;
import com.chinamobile.sparrow.ai.service.dify.ConsoleFacade;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.PaginatedRecords;
import com.chinamobile.sparrow.domain.lang.ResultWarpperRuntimeException;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.google.common.reflect.TypeToken;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.jinq.tuples.Pair;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.util.*;
import java.util.stream.Collectors;

@Repository
public class DefaultApplicationRepository extends ApplicationRepository<DefaultApplication> {

    public DefaultApplicationRepository(
            @Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
            ConsoleFacade consoleFacade
    ) {
        super(entityManagerFactory, jinqJPAStreamProvider, DefaultApplication.class, consoleFacade);
    }

    @Override
    public Result<DefaultApplication> get(String id) {
        Result<DefaultApplication> _record = new Result<>();

        _record.data = getCurrentSession().get(DefaultApplication.class, id);
        if (_record.data == null) {
            _record.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{this.tClass.getSimpleName()});
        } else {
            if (_record.data instanceof DatasetApplication) {
                parseDatasets(Collections.singletonList(_record.data));
            } else if (_record.data instanceof WorkflowApplication) {
                parseApplication(Collections.singletonList(_record.data));
            }
        }

        return _record;
    }

    @Override
    @Deprecated
    public Result<String> save(DefaultApplication record, String operatorId) {
        throw new UnsupportedOperationException();
    }

    @Override
    @Transactional
    public Result<Void> remove(String id, String operatorId) {
        Result<Void> _success = new Result<>();

        Result<DefaultApplication> _record = ((DefaultApplicationRepository) AopContext.currentProxy()).permit(id, operatorId);
        if (!_record.isOK()) {
            return _success.pack(_record);
        }

        // 删除授权
        getCurrentSession().createQuery("delete from ApplicationScope where applicationId = :id")
                .setParameter("id", id)
                .executeUpdate();

        // 删除应用
        getCurrentSession().remove(_record.data);

        if (_record.data instanceof WorkflowApplication && Application.ENUM_PLATFORM.DIFY == _record.data.getPlatform() && StringUtils.hasLength(_record.data.getPlatformId())) {
            _success = consoleFacade.removeApp(_record.data.getPlatformId());
            if (!_success.isOK()) {
                throw new ResultWarpperRuntimeException(_success);
            }
        }

        return _success;
    }

    @Transactional(readOnly = true)
    public PaginatedRecords<DefaultApplication> search(int count, int index, String name, String userId) {
        PaginatedRecords<DefaultApplication> _page = super.search(count, index, name);
        parseDatasets(_page.records);
        parseOwner(_page.records, userId);
        return _page;
    }

    public Result<String> save(DatasetApplication record, String operatorId) {
        Result<String> _id = new Result<>();

        boolean _alreadyExisted = true;

        String _identifier = record.getId();
        String _name = record.getName();
        DatasetApplication _record = stream(DatasetApplication.class).where(i -> _identifier.equals(i.getId()))
                .findFirst().orElse(null);
        if (_record == null) {
            _record = new DatasetApplication();

            _alreadyExisted = false;
        }

        if (stream(DatasetApplication.class).where(i -> !_identifier.equals(i.getId()) && _name.equals(i.getName())).findFirst().isPresent()) {
            _id.setCode(Result.DATABASE_RECORD_ALREADY_EXIST);
            return _id;
        }

        copyProperties(record, _record, new String[]{"id", "platform", "platformId"});

        _record.setDatasetsKey(CollectionUtils.isEmpty(record.getDatasets()) ? null : ConverterUtil.toJson(record.getDatasets()));

        if (_alreadyExisted) {
            update(_record, operatorId);
        } else {
            add(_record, operatorId);
        }

        _id.data = _record.getId();
        return _id;
    }

    public Result<String> save(LinkApplication record, String operatorId) {
        Result<String> _id = new Result<>();

        boolean _alreadyExisted = true;

        String _identifier = record.getId();
        String _name = record.getName();
        LinkApplication _record = stream(LinkApplication.class).where(i -> _identifier.equals(i.getId()))
                .findFirst().orElse(null);
        if (_record == null) {
            _record = new LinkApplication();

            _alreadyExisted = false;
        }

        if (stream(LinkApplication.class).where(i -> !_identifier.equals(i.getId()) && _name.equals(i.getName()))
                .findFirst().isPresent()) {
            _id.setCode(Result.DATABASE_RECORD_ALREADY_EXIST);
            return _id;
        }

        copyProperties(record, _record, new String[]{"id", "platform", "platformId"});

        if (_alreadyExisted) {
            update(_record, operatorId);
        } else {
            add(_record, operatorId);
        }

        _id.data = _record.getId();
        return _id;
    }

    public Result<String> save(PromptApplication record, String operatorId) {
        Result<String> _id = new Result<>();

        boolean _alreadyExisted = true;

        String _identifier = record.getId();
        String _name = record.getName();
        PromptApplication _record = stream(PromptApplication.class).where(i -> _identifier.equals(i.getId()))
                .findFirst().orElse(null);
        if (_record == null) {
            _record = new PromptApplication();

            _alreadyExisted = false;
        }

        if (stream(PromptApplication.class).where(i -> !_identifier.equals(i.getId()) && _name.equals(i.getName())).findFirst().isPresent()) {
            _id.setCode(Result.DATABASE_RECORD_ALREADY_EXIST);
            return _id;
        }

        copyProperties(record, _record, new String[]{"id", "platform", "platformId"});

        if (_alreadyExisted) {
            update(_record, operatorId);
        } else {
            add(_record, operatorId);
        }

        _id.data = _record.getId();
        return _id;
    }

    public Result<String> save(WorkflowApplication record, String operatorId) {
        Result<String> _id = new Result<>();

        String _platformId = record.getPlatformId();
        Application.ENUM_PLATFORM _platform = record.getPlatform();
        WorkflowApplication _record = stream(WorkflowApplication.class).where(i -> _platform == i.getPlatform() && _platformId.equals(i.getPlatformId()))
                .findFirst().orElse(null);
        if (_record == null) {
            _record = new WorkflowApplication(_platform, _platformId);
            add(_record, operatorId);
        }

        _id.data = _record.getId();
        return _id;
    }

    @Transactional(readOnly = true)
    public List<DefaultApplication> searchUserApplications(String type, String name, User user) {
        JinqStream<? extends DefaultApplication> _query;
        switch (type) {
            case "dataset":
                _query = stream(DatasetApplication.class);
                break;
            case "link":
                _query = stream(LinkApplication.class);
                break;
            case "prompt":
                _query = stream(PromptApplication.class);
                break;
            case "workflow":
                _query = stream(WorkflowApplication.class);
                break;
            default:
                _query = stream(DefaultApplication.class);
                break;
        }

        String _userId = user.getId();
        List<String> _deptIds = StringUtils.hasLength(user.getDeptCode())
                ? Arrays.asList(user.getDeptCode().split("\\" + Department.CODE_SEPARATOR))
                : new ArrayList<>();
        ApplicationScope.ENUM_TYPE _deptScope = ApplicationScope.ENUM_TYPE.DEPARTMENT;
        ApplicationScope.ENUM_TYPE _userScope = ApplicationScope.ENUM_TYPE.USER;

        _query = _query.leftOuterJoin((i, source) -> source.stream(ApplicationScope.class), (i, j) -> j.getApplicationId().equals(i.getId()))
                .where(i -> _userId.equals(i.getOne().getCreatorId()) || (i.getTwo() != null && (
                                (i.getTwo().getType() == _deptScope && _deptIds.contains(i.getTwo().getTargetId())) || (_userScope == i.getTwo().getType() && _userId.equals(i.getTwo().getTargetId()))
                        ))
                )
                .select(Pair::getOne);

        if (StringUtils.hasLength(name)) {
            _query = _query.where(i -> i.getName().contains(name));
        }

        List<DefaultApplication> _records = (List<DefaultApplication>) _query.toList();
        parseApplication(_records);
        parseDatasets(_records);
        parseOwner(_records, _userId);
        return _records;
    }

    public Result<String> addScope(String id, ApplicationScope.ENUM_TYPE type, String targetId, String operatorId) {
        Result<String> _id = new Result<>();

        Result<Application> _record = ((ApplicationRepository<Application>) AopContext.currentProxy()).get(id);
        if (!_record.isOK()) {
            return _id.pack(_record);
        }

        ApplicationScope _scope = stream(ApplicationScope.class)
                .where(i -> i.getApplicationId().equals(id) && i.getType() == type && i.getTargetId().equals(targetId))
                .findFirst().orElse(null);
        if (_scope == null) {
            _scope = new ApplicationScope();
            _scope.setApplicationId(id);
            _scope.setType(type);
            _scope.setTargetId(targetId);
            _scope.setCreatorId(operatorId);
            _scope.setCreateTime(new Date());

            getCurrentSession().save(_scope);
        }

        _id.data = _scope.getId();
        return _id;
    }

    @Transactional(readOnly = true)
    public PaginatedRecords<ApplicationScope> searchScopes(int count, int index, String id, String type, String name) {
        JinqStream<ApplicationScope> _temp = stream(ApplicationScope.class).where(i -> id.equals(i.getApplicationId()));

        if (StringUtils.hasLength(type)) {
            switch (type) {
                case "dataset":
                    _temp = _temp.leftOuterJoin((i, source) -> source.stream(DatasetApplication.class), (i, j) -> j.getId().equals(i.getApplicationId()))
                            .where(i -> i.getTwo() != null)
                            .select(Pair::getOne);
                    break;
                case "link":
                    _temp = _temp.leftOuterJoin((i, source) -> source.stream(LinkApplication.class), (i, j) -> j.getId().equals(i.getApplicationId()))
                            .where(i -> i.getTwo() != null)
                            .select(Pair::getOne);
                    break;
                case "prompt":
                    _temp = _temp.leftOuterJoin((i, source) -> source.stream(PromptApplication.class), (i, j) -> j.getId().equals(i.getApplicationId()))
                            .where(i -> i.getTwo() != null)
                            .select(Pair::getOne);
                    break;
                case "workflow":
                    _temp = _temp.leftOuterJoin((i, source) -> source.stream(WorkflowApplication.class), (i, j) -> j.getId().equals(i.getApplicationId()))
                            .where(i -> i.getTwo() != null)
                            .select(Pair::getOne);
                    break;
                default:
                    _temp = _temp.leftOuterJoin((i, source) -> source.stream(DefaultApplication.class), (i, j) -> j.getId().equals(i.getApplicationId()))
                            .where(i -> i.getTwo() != null)
                            .select(Pair::getOne);
                    break;
            }
        }

        ApplicationScope.ENUM_TYPE _deptScope = ApplicationScope.ENUM_TYPE.DEPARTMENT;
        ApplicationScope.ENUM_TYPE _userScope = ApplicationScope.ENUM_TYPE.USER;
        JinqStream<Pair<Pair<ApplicationScope, Department>, User>> _query = _temp.leftOuterJoin((i, source) -> source.stream(Department.class), (i, j) -> _deptScope == i.getType() && j.getId().equals(i.getTargetId()))
                .leftOuterJoin((i, source) -> source.stream(User.class), (i, j) -> _userScope == i.getOne().getType() && j.getId().equals(i.getOne().getTargetId()));

        _query = StringUtils.hasLength(name)
                ? _query.where(i -> (_deptScope == i.getOne().getOne().getType() && i.getOne().getTwo().getName().contains(name)) || (_userScope == i.getOne().getOne().getType() && i.getTwo().getName().contains(name)))
                : _query.where(i -> i.getOne().getTwo() != null || i.getTwo() != null);

        PaginatedRecords<ApplicationScope> _page = new PaginatedRecords<>(count, index);
        _page.total = _query.count();

        if (count >= 0 && index >= 0) {
            _query = _query.skip((long) count * index).limit(count);
        }

        _page.records = _query.toList().stream()
                .map(i -> {
                    ApplicationScope _scope = i.getOne().getOne();
                    _scope.setName(i.getOne().getTwo() != null ? i.getOne().getTwo().getName() : i.getTwo().getName());
                    return _scope;
                })
                .collect(Collectors.toList());
        return _page;
    }

    public Result<Void> removeScope(String scopeId) {
        Result<Void> _success = new Result<>();

        ApplicationScope _scope = getCurrentSession().get(ApplicationScope.class, scopeId);
        if (_scope == null) {
            _success.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{ApplicationScope.class.getSimpleName()});
            return _success;
        }

        getCurrentSession().remove(_scope);

        return _success;
    }

    @Override
    protected void parseApplication(List<DefaultApplication> records) {
        if (CollectionUtils.isEmpty(records) || records.stream().noneMatch(i -> i instanceof WorkflowApplication)) {
            return;
        }

        super.parseApplication(records);
    }

    void parseDatasets(List<? extends DefaultApplication> records) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }

        records.stream()
                .filter(i -> i instanceof DatasetApplication)
                .forEach(i -> {
                    ((DatasetApplication) i).setDatasets(StringUtils.hasLength(((DatasetApplication) i).getDatasetsKey()) ? ConverterUtil.json2Object(((DatasetApplication) i).getDatasetsKey(), new TypeToken<List<String>>() {
                    }.getType()) : null);
                });
    }

    void parseOwner(List<? extends DefaultApplication> records, String userId) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }

        records.forEach(i -> {
            i.setIsOwner(Objects.equals(i.getCreatorId(), userId));
        });
    }

}