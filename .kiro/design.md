# Dubhe AI平台技术设计文档

## 系统架构设计

### 整体架构

Dubhe采用分层微服务架构，基于Spring Boot构建，支持水平扩展和高可用部署。

```mermaid
graph TB
    subgraph "前端层"
        A[Vue.js 管理界面]
        B[移动端 H5]
        C[第三方集成]
    end
    
    subgraph "网关层"
        D[Nginx 负载均衡]
        E[API Gateway]
    end
    
    subgraph "应用层"
        F[认证服务]
        G[对话服务]
        H[知识库服务]
        I[文件服务]
        J[插件服务]
    end
    
    subgraph "数据层"
        K[MySQL 主从]
        L[Redis 集群]
        M[向量数据库]
        N[对象存储]
    end
    
    A --> D
    B --> D
    C --> E
    D --> E
    E --> F
    E --> G
    E --> H
    E --> I
    E --> J
    F --> K
    F --> L
    G --> K
    G --> L
    G --> M
    H --> M
    H --> N
    I --> N
    J --> K
```

### 核心组件设计

#### 1. 认证授权模块

**技术选型**
- 框架：Apache Shiro 1.13.0
- 会话存储：Redis Session Store
- 加密算法：RSA + AES-256
- 令牌机制：JWT + Refresh Token

**组件结构**
```typescript
interface AuthenticationModule {
  // 认证配置
  ShiroConfiguration: {
    realms: AuthorizingRealm[];
    sessionManager: SessionManager;
    securityManager: SecurityManager;
  };
  
  // 认证控制器
  LoginController: {
    login(credentials: LoginRequest): AuthResponse;
    logout(token: string): void;
    refresh(refreshToken: string): AuthResponse;
  };
  
  // 用户仓储
  UserRepository: {
    findByUsername(username: string): User;
    validatePassword(user: User, password: string): boolean;
    updateLastLogin(userId: string): void;
  };
  
  // 权限管理
  PermissionRepository: {
    getUserPermissions(userId: string): Permission[];
    checkPermission(userId: string, resource: string): boolean;
  };
}
```

**安全策略**
- 密码RSA加密传输，AES-256存储加密
- 会话超时控制（30分钟滑动窗口）
- 登录失败锁定（3次失败锁定15分钟）
- 细粒度权限控制（资源级别）

#### 2. 大模型对话模块

**核心组件**
```typescript
interface ConversationModule {
  // 对话控制器
  ConversationController: {
    createConversation(request: CreateConversationRequest): Conversation;
    sendMessage(conversationId: string, message: Message): MessageResponse;
    getHistory(conversationId: string, pagination: Pagination): MessageList;
  };
  
  // 对话服务
  ConversationService: {
    processMessage(conversation: Conversation, message: Message): Promise<Response>;
    manageMemory(conversationId: string): void;
    applyPlugins(message: Message): EnhancedMessage;
  };
  
  // 消息处理器
  MessageProcessor: {
    preprocess(message: Message): ProcessedMessage;
    callLLM(processedMessage: ProcessedMessage): LLMResponse;
    postprocess(response: LLMResponse): FinalResponse;
  };
  
  // 插件管理器
  PluginManager: {
    loadPlugins(): Plugin[];
    executePlugin(plugin: Plugin, context: PluginContext): PluginResult;
    handlePluginError(error: PluginError): void;
  };
}
```

**插件架构**
- **ParserPlugin**: 文档解析插件（PDF、Word、Excel等）
- **WebSearchPlugin**: 网络搜索插件（Bing Search API）
- **CustomPlugin**: 自定义扩展插件接口

**记忆管理**
- 最大容量：50条对话记录
- 存储方式：Redis缓存，键前缀"dubhe:llm:memory:"
- 压缩策略：超出容量时智能摘要压缩

#### 3. 知识库管理模块

**数据流程**
```mermaid
flowchart LR
    A[文档上传] --> B[格式验证]
    B --> C[内容解析]
    C --> D[文本分块]
    D --> E[向量化]
    E --> F[索引存储]
    F --> G[检索服务]
    
    subgraph "解析器"
        C1[PDF解析器]
        C2[Word解析器]
        C3[Excel解析器]
        C4[Markdown解析器]
    end
    
    C --> C1
    C --> C2
    C --> C3
    C --> C4
```

**支持格式**
- 文本类：txt, markdown, md, mdx, html, htm
- 文档类：pdf, docx, xlsx, xls, csv
- 其他：可通过插件扩展

**检索策略**
```typescript
interface RetrievalConfig {
  mode: 'multiple' | 'single';
  topK: number; // 默认4
  scoreThreshold: number; // 默认0.3
  rerankingModel: {
    name: string;
    provider: string;
  };
}
```

#### 4. 文件管理模块

**存储策略**
```typescript
interface FileStorageStrategy {
  // 本地存储
  localStorage: {
    basePath: string;
    partitionStrategy: 'date' | 'hash' | 'size';
  };
  
  // 对象存储
  objectStorage: {
    provider: 'S3' | 'OSS' | 'COS';
    bucket: string;
    region: string;
  };
  
  // 分片上传
  chunkUpload: {
    chunkSize: number; // 1MB
    maxRetries: number; // 3
    resumable: boolean; // true
  };
}
```

**文件类型控制**
```yaml
file:
  extension:
    allowed: "txt,doc,docx,xls,xlsx,ppt,pptx,ofd,pdf,avif,bmp,gif,ico,jfif,jpeg,jpg,jxl,pjp,pjpeg,png,svg,svgz,tif,tiff,webp,xbm,asx,avi,m4v,mov,mp4,mpeg,mpg,ogm,ogv,webm,wmv,rar,zip"
    forbidden: "exe,bat,sh,cmd"
  part:
    size: 1048576 # 1MB
  max-size: 104857600 # 100MB
```

## 数据库设计

### 主要数据表结构

#### 用户认证相关表
```sql
-- 用户表
CREATE TABLE users (
    id VARCHAR(32) PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    phone VARCHAR(20),
    status ENUM('ACTIVE', 'INACTIVE', 'LOCKED') DEFAULT 'ACTIVE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login_at TIMESTAMP,
    login_attempts INT DEFAULT 0,
    locked_until TIMESTAMP NULL
);

-- 角色表
CREATE TABLE roles (
    id VARCHAR(32) PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 权限表
CREATE TABLE permissions (
    id VARCHAR(32) PRIMARY KEY,
    resource VARCHAR(100) NOT NULL,
    action VARCHAR(50) NOT NULL,
    description TEXT,
    UNIQUE KEY uk_resource_action (resource, action)
);

-- 用户角色关联表
CREATE TABLE user_roles (
    user_id VARCHAR(32),
    role_id VARCHAR(32),
    PRIMARY KEY (user_id, role_id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (role_id) REFERENCES roles(id)
);
```

#### LLM应用相关表
```sql
-- 应用表
CREATE TABLE applications (
    id VARCHAR(32) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    model_config JSON,
    prompt_template TEXT,
    user_id VARCHAR(32),
    status ENUM('DRAFT', 'PUBLISHED', 'ARCHIVED') DEFAULT 'DRAFT',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 对话表
CREATE TABLE conversations (
    id VARCHAR(32) PRIMARY KEY,
    application_id VARCHAR(32),
    user_id VARCHAR(32),
    title VARCHAR(200),
    status ENUM('ACTIVE', 'ARCHIVED') DEFAULT 'ACTIVE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (application_id) REFERENCES applications(id),
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 消息表
CREATE TABLE messages (
    id VARCHAR(32) PRIMARY KEY,
    conversation_id VARCHAR(32),
    role ENUM('user', 'assistant', 'system') NOT NULL,
    content TEXT NOT NULL,
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (conversation_id) REFERENCES conversations(id)
);
```

#### 知识库相关表
```sql
-- 知识库表
CREATE TABLE datasets (
    id VARCHAR(32) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    user_id VARCHAR(32),
    config JSON,
    status ENUM('ACTIVE', 'BUILDING', 'ERROR') DEFAULT 'BUILDING',
    document_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 文档表
CREATE TABLE documents (
    id VARCHAR(32) PRIMARY KEY,
    dataset_id VARCHAR(32),
    name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500),
    file_size BIGINT,
    file_type VARCHAR(50),
    content_hash VARCHAR(64),
    chunk_count INT DEFAULT 0,
    status ENUM('PROCESSING', 'COMPLETED', 'ERROR') DEFAULT 'PROCESSING',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (dataset_id) REFERENCES datasets(id)
);

-- 文档块表
CREATE TABLE document_chunks (
    id VARCHAR(32) PRIMARY KEY,
    document_id VARCHAR(32),
    content TEXT NOT NULL,
    vector_data JSON,
    position INT,
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (document_id) REFERENCES documents(id)
);
```

## API设计规范

### RESTful API标准

#### 基础路径和版本控制
```
Base URL: /api/v1/
API Version: v1 (当前版本)
Content-Type: application/json
```

#### 认证方式
```http
Authorization: Bearer <JWT_TOKEN>
X-Session-ID: <SESSION_ID>
```

#### 统一响应格式
```typescript
interface ApiResponse<T> {
  code: string;           // 响应码
  message: string;        // 响应消息
  data: T;               // 响应数据
  timestamp: string;      // 时间戳
  requestId: string;      // 请求ID
}

interface ErrorResponse {
  code: string;
  message: string;
  details?: any;
  timestamp: string;
  requestId: string;
}
```

#### 主要API端点

**认证相关**
```typescript
// 用户登录
POST /sec/login
Request: {
  username: string;
  password: string; // RSA加密
  captcha?: string;
}
Response: {
  token: string;
  refreshToken: string;
  expiresIn: number;
  user: UserInfo;
}

// 用户登出
POST /sec/logout
Request: {}
Response: {}

// 刷新令牌
POST /sec/refresh
Request: {
  refreshToken: string;
}
Response: {
  token: string;
  expiresIn: number;
}
```

**对话相关**
```typescript
// 获取对话列表
GET /llm/conversations?page=1&size=20&status=ACTIVE
Response: {
  items: Conversation[];
  total: number;
  page: number;
  size: number;
}

// 创建对话
POST /llm/conversations
Request: {
  applicationId: string;
  title?: string;
}
Response: Conversation

// 发送消息
POST /llm/conversations/{id}/messages
Request: {
  content: string;
  attachments?: FileAttachment[];
}
Response: {
  messageId: string;
  response: string;
  usage: TokenUsage;
}
```

**知识库相关**
```typescript
// 获取知识库列表
GET /llm/datasets?page=1&size=20
Response: {
  items: Dataset[];
  total: number;
}

// 创建知识库
POST /llm/datasets
Request: {
  name: string;
  description?: string;
  config?: DatasetConfig;
}
Response: Dataset

// 上传文档
POST /llm/datasets/{id}/documents
Request: FormData {
  file: File;
  metadata?: string;
}
Response: {
  documentId: string;
  status: string;
}
```

## 技术选型说明

### 后端技术栈
- **框架**：Spring Boot 2.7.18
- **数据库**：MySQL 8.4.0 + ShardingSphere
- **缓存**：Redis 6.0+
- **安全**：Apache Shiro 1.13.0
- **ORM**：Hibernate + Jinq
- **构建工具**：Maven 3.8+
- **JDK版本**：Java 8+

### 前端技术栈
- **框架**：基于Sparrow前端框架
- **UI组件**：Ant Design Vue
- **状态管理**：Vuex/Pinia
- **HTTP客户端**：Axios
- **构建工具**：Vite

### 第三方服务集成
- **大模型**：支持OpenAI、Claude、文心一言等
- **搜索引擎**：Bing Search API
- **语音服务**：Microsoft Cognitive Services
- **地图服务**：高德地图、腾讯地图
- **通信服务**：微信公众平台、企业微信

### 基础设施
- **容器化**：Docker + Docker Compose
- **负载均衡**：Nginx
- **监控**：Spring Boot Actuator + Prometheus
- **日志**：Logback + ELK Stack
- **CI/CD**：Jenkins/GitLab CI
