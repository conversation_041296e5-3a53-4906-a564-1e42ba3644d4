# Dubhe AI平台项目结构规范

## 项目目录结构

### 根目录结构
```
dubhe-api/
├── .kiro/                          # Kiro规格文档目录
│   ├── requirements.md             # 需求规格文档
│   ├── design.md                   # 技术设计文档
│   ├── tasks.md                    # 任务实施文档
│   └── steering/                   # 项目指导文档
│       ├── product.md              # 产品规格
│       ├── tech.md                 # 技术规格
│       └── structure.md            # 结构规范
├── api/                            # API模块
├── infra/                          # 基础设施模块
├── docs/                           # 项目文档
├── scripts/                        # 构建和部署脚本
├── docker/                         # Docker配置文件
├── k8s/                           # Kubernetes配置文件
├── pom.xml                        # Maven根配置
├── application.yml                # 应用配置
├── shardingsphere.yml            # 分库分表配置
├── logback-spring.xml            # 日志配置
├── README.md                      # 项目说明
├── CHANGELOG.md                   # 变更日志
└── .gitignore                     # Git忽略文件
```

### API模块结构
```
api/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/chinamobile/si/dubhe/
│   │   │       ├── Application.java           # 启动类
│   │   │       ├── BeanConfiguration.java     # Bean配置
│   │   │       ├── ShiroConfiguration.java    # 安全配置
│   │   │       ├── controller/                # 控制器层
│   │   │       │   ├── sec/                   # 安全相关控制器
│   │   │       │   ├── llm/                   # LLM相关控制器
│   │   │       │   ├── sys/                   # 系统管理控制器
│   │   │       │   └── media/                 # 媒体文件控制器
│   │   │       ├── service/                   # 服务层
│   │   │       │   ├── impl/                  # 服务实现
│   │   │       │   └── facade/                # 外部服务门面
│   │   │       ├── dto/                       # 数据传输对象
│   │   │       │   ├── request/               # 请求DTO
│   │   │       │   ├── response/              # 响应DTO
│   │   │       │   └── common/                # 通用DTO
│   │   │       ├── config/                    # 配置类
│   │   │       │   ├── database/              # 数据库配置
│   │   │       │   ├── cache/                 # 缓存配置
│   │   │       │   ├── security/              # 安全配置
│   │   │       │   └── web/                   # Web配置
│   │   │       ├── exception/                 # 异常处理
│   │   │       │   ├── handler/               # 全局异常处理器
│   │   │       │   └── custom/                # 自定义异常
│   │   │       ├── util/                      # 工具类
│   │   │       │   ├── security/              # 安全工具
│   │   │       │   ├── json/                  # JSON工具
│   │   │       │   └── validation/            # 验证工具
│   │   │       └── constant/                  # 常量定义
│   │   │           ├── ErrorCode.java         # 错误码
│   │   │           ├── ApiPath.java           # API路径
│   │   │           └── CacheKey.java          # 缓存键
│   │   └── resources/
│   │       ├── application.yml                # 应用配置
│   │       ├── application-dev.yml            # 开发环境配置
│   │       ├── application-test.yml           # 测试环境配置
│   │       ├── application-prod.yml           # 生产环境配置
│   │       ├── logback-spring.xml             # 日志配置
│   │       ├── static/                        # 静态资源
│   │       └── templates/                     # 模板文件
│   └── test/
│       ├── java/                              # 测试代码
│       │   └── com/chinamobile/si/dubhe/
│       │       ├── controller/                # 控制器测试
│       │       ├── service/                   # 服务测试
│       │       ├── repository/                # 仓储测试
│       │       └── integration/               # 集成测试
│       └── resources/                         # 测试资源
│           ├── application-test.yml           # 测试配置
│           └── data/                          # 测试数据
└── pom.xml                                    # Maven配置
```

### INFRA模块结构
```
infra/
├── src/
│   ├── main/
│   │   └── java/
│   │       └── com/chinamobile/sparrow/
│   │           ├── domain/                    # 领域层
│   │           │   ├── entity/                # 实体类
│   │           │   │   ├── sec/               # 安全实体
│   │           │   │   ├── llm/               # LLM实体
│   │           │   │   ├── sys/               # 系统实体
│   │           │   │   └── media/             # 媒体实体
│   │           │   ├── repository/            # 仓储接口
│   │           │   │   ├── sec/               # 安全仓储
│   │           │   │   ├── llm/               # LLM仓储
│   │           │   │   ├── sys/               # 系统仓储
│   │           │   │   └── media/             # 媒体仓储
│   │           │   ├── service/               # 领域服务
│   │           │   │   ├── sec/               # 安全服务
│   │           │   │   ├── llm/               # LLM服务
│   │           │   │   ├── search/            # 搜索服务
│   │           │   │   └── wx/                # 微信服务
│   │           │   └── infra/                 # 基础设施
│   │           │       ├── sec/               # 安全基础设施
│   │           │       ├── job/               # 任务调度
│   │           │       ├── cache/             # 缓存
│   │           │       └── mq/                # 消息队列
│   │           └── springboot/                # Spring Boot集成
│   │               └── web/
│   │                   └── controller/        # 基础控制器
│   └── test/
│       └── java/                              # 测试代码
└── pom.xml                                    # Maven配置
```

## 命名规范

### 包命名规范
```java
// 基础包结构
com.chinamobile.si.dubhe                      // 根包
├── controller                                 // 控制器包
│   ├── sec                                   // 安全控制器
│   ├── llm                                   // LLM控制器
│   ├── sys                                   // 系统控制器
│   └── media                                 // 媒体控制器
├── service                                   // 服务包
│   ├── impl                                  // 服务实现
│   └── facade                                // 外部服务门面
├── dto                                       // 数据传输对象
│   ├── request                               // 请求DTO
│   ├── response                              // 响应DTO
│   └── common                                // 通用DTO
├── config                                    // 配置包
├── exception                                 // 异常包
├── util                                      // 工具包
└── constant                                  // 常量包
```

### 类命名规范
```java
// 控制器命名
public class ConversationController {}         // 对话控制器
public class DatasetController {}              // 知识库控制器
public class UserController {}                 // 用户控制器

// 服务命名
public interface ConversationService {}        // 对话服务接口
public class ConversationServiceImpl {}        // 对话服务实现
public class DifyConsoleFacade {}             // Dify控制台门面

// 实体命名
public class Conversation {}                   // 对话实体
public class Message {}                        // 消息实体
public class Dataset {}                        // 知识库实体

// DTO命名
public class CreateConversationRequest {}      // 创建对话请求
public class ConversationResponse {}           // 对话响应
public class MessageDTO {}                     // 消息DTO

// 仓储命名
public interface ConversationRepository {}     // 对话仓储接口
public class ConversationRepositoryImpl {}     // 对话仓储实现

// 配置命名
public class DatabaseConfiguration {}          // 数据库配置
public class CacheConfiguration {}             // 缓存配置
public class SecurityConfiguration {}          // 安全配置
```

### 方法命名规范
```java
// 控制器方法命名
@GetMapping("/conversations")
public Result<List<ConversationDTO>> getConversations() {}

@PostMapping("/conversations")
public Result<ConversationDTO> createConversation() {}

@PutMapping("/conversations/{id}")
public Result<ConversationDTO> updateConversation() {}

@DeleteMapping("/conversations/{id}")
public Result<Void> deleteConversation() {}

// 服务方法命名
public ConversationDTO createConversation(CreateConversationRequest request) {}
public ConversationDTO findConversationById(String id) {}
public List<ConversationDTO> findConversationsByUserId(String userId) {}
public void updateConversation(String id, UpdateConversationRequest request) {}
public void deleteConversation(String id) {}

// 仓储方法命名
public Optional<Conversation> findById(String id) {}
public List<Conversation> findByUserId(String userId) {}
public Conversation save(Conversation conversation) {}
public void deleteById(String id) {}
public boolean existsById(String id) {}
```

### 变量命名规范
```java
// 常量命名（全大写，下划线分隔）
public static final String DEFAULT_PAGE_SIZE = "20";
public static final int MAX_RETRY_TIMES = 3;
public static final long SESSION_TIMEOUT = 30 * 60 * 1000L;

// 变量命名（驼峰命名）
private String conversationId;
private List<MessageDTO> messageList;
private ConversationService conversationService;
private UserRepository userRepository;

// 方法参数命名
public ConversationDTO createConversation(
    CreateConversationRequest request,
    String userId,
    Authentication authentication
) {}

// 局部变量命名
String conversationId = request.getConversationId();
List<Message> messageList = messageRepository.findByConversationId(conversationId);
ConversationDTO conversationDTO = ConversationMapper.toDTO(conversation);
```

## 文件组织规范

### 配置文件组织
```yaml
# application.yml - 主配置文件
spring:
  profiles:
    active: dev
  application:
    name: dubhe-api

# application-dev.yml - 开发环境配置
spring:
  datasource:
    url: *************************************
    username: dev_user
    password: dev_password

# application-test.yml - 测试环境配置
spring:
  datasource:
    url: ************************************
    username: test_user
    password: test_password

# application-prod.yml - 生产环境配置
spring:
  datasource:
    url: ************************************
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
```

### 静态资源组织
```
static/
├── css/                           # 样式文件
│   ├── common.css                 # 通用样式
│   └── admin.css                  # 管理后台样式
├── js/                            # JavaScript文件
│   ├── common.js                  # 通用脚本
│   └── admin.js                   # 管理后台脚本
├── images/                        # 图片资源
│   ├── logo/                      # Logo图片
│   ├── icons/                     # 图标
│   └── backgrounds/               # 背景图片
├── fonts/                         # 字体文件
└── docs/                          # 文档资源
    ├── api/                       # API文档
    └── user/                      # 用户手册
```

### 测试文件组织
```
test/
├── java/
│   └── com/chinamobile/si/dubhe/
│       ├── controller/            # 控制器测试
│       │   ├── ConversationControllerTest.java
│       │   └── DatasetControllerTest.java
│       ├── service/               # 服务测试
│       │   ├── ConversationServiceTest.java
│       │   └── DatasetServiceTest.java
│       ├── repository/            # 仓储测试
│       │   ├── ConversationRepositoryTest.java
│       │   └── DatasetRepositoryTest.java
│       ├── integration/           # 集成测试
│       │   ├── ConversationIntegrationTest.java
│       │   └── SecurityIntegrationTest.java
│       └── util/                  # 工具类测试
│           ├── JsonUtilTest.java
│           └── SecurityUtilTest.java
└── resources/
    ├── application-test.yml       # 测试配置
    ├── data/                      # 测试数据
    │   ├── conversations.json     # 对话测试数据
    │   └── users.json             # 用户测试数据
    └── sql/                       # 测试SQL
        ├── schema.sql             # 表结构
        └── data.sql               # 初始数据
```

## 代码组织原则

### 分层架构原则
```java
// 控制器层 - 处理HTTP请求
@RestController
@RequestMapping("/api/v1/llm")
public class ConversationController {
    
    private final ConversationService conversationService;
    
    // 只处理HTTP相关逻辑，业务逻辑委托给服务层
    @PostMapping("/conversations")
    public Result<ConversationDTO> createConversation(
            @Valid @RequestBody CreateConversationRequest request) {
        ConversationDTO conversation = conversationService.createConversation(request);
        return Result.success(conversation);
    }
}

// 服务层 - 处理业务逻辑
@Service
@Transactional
public class ConversationServiceImpl implements ConversationService {
    
    private final ConversationRepository conversationRepository;
    private final MessageRepository messageRepository;
    
    // 处理复杂业务逻辑，协调多个仓储
    @Override
    public ConversationDTO createConversation(CreateConversationRequest request) {
        // 业务验证
        validateConversationRequest(request);
        
        // 创建对话
        Conversation conversation = new Conversation();
        conversation.setTitle(request.getTitle());
        conversation.setUserId(request.getUserId());
        
        // 保存到数据库
        conversation = conversationRepository.save(conversation);
        
        // 转换为DTO返回
        return ConversationMapper.toDTO(conversation);
    }
}

// 仓储层 - 处理数据访问
@Repository
public class ConversationRepositoryImpl implements ConversationRepository {
    
    private final EntityManager entityManager;
    
    // 只处理数据访问逻辑
    @Override
    public Conversation save(Conversation conversation) {
        if (conversation.getId() == null) {
            entityManager.persist(conversation);
            return conversation;
        } else {
            return entityManager.merge(conversation);
        }
    }
}
```

### 依赖注入原则
```java
// 构造器注入（推荐）
@Service
public class ConversationServiceImpl implements ConversationService {
    
    private final ConversationRepository conversationRepository;
    private final MessageRepository messageRepository;
    private final UserService userService;
    
    // 使用构造器注入，确保依赖不可变
    public ConversationServiceImpl(
            ConversationRepository conversationRepository,
            MessageRepository messageRepository,
            UserService userService) {
        this.conversationRepository = conversationRepository;
        this.messageRepository = messageRepository;
        this.userService = userService;
    }
}

// 字段注入（不推荐，仅在特殊情况下使用）
@Service
public class LegacyService {
    
    @Autowired
    private SomeRepository someRepository;
}
```

### 异常处理原则
```java
// 全局异常处理器
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    @ExceptionHandler(ValidationException.class)
    public Result<Void> handleValidationException(ValidationException e) {
        return Result.error("VALIDATION_ERROR", e.getMessage());
    }
    
    @ExceptionHandler(BusinessException.class)
    public Result<Void> handleBusinessException(BusinessException e) {
        return Result.error(e.getCode(), e.getMessage());
    }
    
    @ExceptionHandler(Exception.class)
    public Result<Void> handleGenericException(Exception e) {
        log.error("Unexpected error", e);
        return Result.error("INTERNAL_ERROR", "系统内部错误");
    }
}

// 自定义业务异常
public class ConversationNotFoundException extends BusinessException {
    
    public ConversationNotFoundException(String conversationId) {
        super("CONVERSATION_NOT_FOUND", "对话不存在: " + conversationId);
    }
}
```

## 版本控制规范

### Git分支策略
```
master/main                        # 主分支，生产环境代码
├── develop                        # 开发分支，集成最新功能
├── feature/conversation-system    # 功能分支，开发新功能
├── feature/knowledge-base         # 功能分支，开发知识库
├── hotfix/security-patch          # 热修复分支，紧急修复
└── release/v1.0.0                 # 发布分支，准备发布
```

### 提交信息规范
```
feat: 添加对话创建功能
fix: 修复用户登录失败问题
docs: 更新API文档
style: 格式化代码
refactor: 重构消息处理逻辑
test: 添加对话服务单元测试
chore: 更新依赖版本
```

### 版本号规范
```
v1.0.0                             # 主版本.次版本.修订版本
├── v1.0.1                         # 修复版本
├── v1.1.0                         # 功能版本
└── v2.0.0                         # 重大版本
```
