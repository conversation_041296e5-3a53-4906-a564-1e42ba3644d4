2025-08-01 15:32:58.208 [DEBUG] @main StartupInfoLogger.logStarting #56: Running with Spring Boot v2.7.18, Spring v5.3.31
2025-08-01 15:33:13.157 [DEBUG] @main IdWorker.getInstance #61: 获取到IPv4地址[127.0.0.1]
2025-08-01 15:33:13.157 [DEBUG] @main IdWorker.getInstance #61: 获取到IPv4地址[*************]
2025-08-01 15:33:13.157 [DEBUG] @main IdWorker.getInstance #73: 设置dataCenterId[1]和workerId[2]
2025-08-01 15:33:21.906 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$85eafc87.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$b7172edd.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.dubhe.Application.main(Application.java:27)
2025-08-01 15:33:21.950 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryConfiguration$EmbeddedUndertow类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$85eafc87.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$b7172edd.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.dubhe.Application.main(Application.java:27)
2025-08-01 15:33:21.975 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.elasticsearch.ElasticsearchRestClientConfigurations$RestHighLevelClientConfiguration类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$85eafc87.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$b7172edd.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.dubhe.Application.main(Application.java:27)
2025-08-01 15:33:21.981 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.data.rest.RepositoryRestMvcAutoConfiguration类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$85eafc87.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$b7172edd.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.dubhe.Application.main(Application.java:27)
2025-08-01 15:33:21.991 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$85eafc87.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$b7172edd.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.dubhe.Application.main(Application.java:27)
2025-08-01 15:33:21.993 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.elasticsearch.ElasticsearchRestClientConfigurations$RestClientFromRestHighLevelClientConfiguration类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$85eafc87.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$b7172edd.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.dubhe.Application.main(Application.java:27)
2025-08-01 15:33:22.014 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.jms.artemis.ArtemisAutoConfiguration类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$85eafc87.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$b7172edd.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.dubhe.Application.main(Application.java:27)
2025-08-01 15:33:22.018 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.elasticsearch.ElasticsearchRestClientConfigurations$RestClientSnifferConfiguration类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$85eafc87.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$b7172edd.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.dubhe.Application.main(Application.java:27)
2025-08-01 15:33:22.033 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryConfiguration$EmbeddedJetty类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$85eafc87.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$b7172edd.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.dubhe.Application.main(Application.java:27)
2025-08-01 15:33:22.036 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.cache.InfinispanCacheConfiguration类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$85eafc87.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$b7172edd.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.dubhe.Application.main(Application.java:27)
2025-08-01 15:33:22.040 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.transaction.jta.AtomikosJtaConfiguration类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$85eafc87.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$b7172edd.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.dubhe.Application.main(Application.java:27)
2025-08-01 15:33:22.077 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.jms.activemq.ActiveMQAutoConfiguration类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$85eafc87.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$b7172edd.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.dubhe.Application.main(Application.java:27)
2025-08-01 15:33:22.080 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.cache.HazelcastCacheConfiguration类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$85eafc87.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$b7172edd.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.dubhe.Application.main(Application.java:27)
2025-08-01 15:33:22.082 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.sql.init.R2dbcInitializationConfiguration类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$85eafc87.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$b7172edd.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.dubhe.Application.main(Application.java:27)
2025-08-01 15:33:22.099 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.cache.Cache2kCacheConfiguration类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$85eafc87.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$b7172edd.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.dubhe.Application.main(Application.java:27)
2025-08-01 15:33:22.110 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.cache.CouchbaseCacheConfiguration类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$85eafc87.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$b7172edd.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.dubhe.Application.main(Application.java:27)
2025-08-01 15:33:22.114 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.cache.EhCacheCacheConfiguration类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$85eafc87.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$b7172edd.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.dubhe.Application.main(Application.java:27)
2025-08-01 15:33:22.120 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.cache.JCacheCacheConfiguration类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$85eafc87.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$b7172edd.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.dubhe.Application.main(Application.java:27)
2025-08-01 15:33:22.124 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.liquibase.LiquibaseAutoConfiguration类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$85eafc87.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$b7172edd.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.dubhe.Application.main(Application.java:27)
