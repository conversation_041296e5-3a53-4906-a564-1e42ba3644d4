package com.chinamobile.si.dubhe;

import cn.binarywang.wx.miniapp.api.WxMaService;
import com.chinamobile.sparrow.ai.infra.llm.plugin.IPlugin;
import com.chinamobile.sparrow.ai.infra.llm.plugin.ParserPlugin;
import com.chinamobile.sparrow.ai.infra.llm.plugin.WebSearchPlugin;
import com.chinamobile.sparrow.ai.model.llm.Conversation;
import com.chinamobile.sparrow.ai.model.llm.Memory;
import com.chinamobile.sparrow.ai.model.llm.Model;
import com.chinamobile.sparrow.ai.repository.llm.ConversationRepository;
import com.chinamobile.sparrow.ai.repository.llm.MemoryRepository;
import com.chinamobile.sparrow.ai.repository.llm.ModelRepository;
import com.chinamobile.sparrow.ai.service.dify.ConsoleFacade;
import com.chinamobile.sparrow.ai.service.dify.DatasetFacade;
import com.chinamobile.sparrow.ai.service.dify.LoginFacade;
import com.chinamobile.sparrow.ai.service.dify.infra.TokenStore;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.repository.media.AbstractMediaRepository;
import com.chinamobile.sparrow.domain.repository.sec.PermissionRepository;
import com.chinamobile.sparrow.domain.repository.sec.RoleRepository;
import com.chinamobile.sparrow.domain.repository.sec.VerificationCodeRepository;
import com.chinamobile.sparrow.domain.repository.sys.*;
import com.chinamobile.sparrow.domain.service.search.BingSearchFacade;
import com.chinamobile.sparrow.domain.service.search.IWebSearchService;
import com.chinamobile.sparrow.domain.service.wx.ma.AccessFacade;
import com.chinamobile.sparrow.domain.service.wx.ma.DefaultAccessFacade;
import com.chinamobile.sparrow.springboot.web.controller.ai.DocumentController;
import com.chinamobile.sparrow.springboot.web.controller.ai.ModelController;
import com.chinamobile.sparrow.springboot.web.controller.ai.PluginController;
import com.chinamobile.sparrow.springboot.web.controller.media.DefaultReaderController;
import com.chinamobile.sparrow.springboot.web.controller.media.DefaultRecordController;
import com.chinamobile.sparrow.springboot.web.controller.media.ReaderController;
import com.chinamobile.sparrow.springboot.web.controller.media.RecordController;
import com.chinamobile.sparrow.springboot.web.controller.sec.DefaultLoginController;
import com.chinamobile.sparrow.springboot.web.controller.sec.LoginController;
import com.chinamobile.sparrow.springboot.web.controller.sys.*;
import okhttp3.ConnectionPool;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.connection.RedisConnectionFactory;

import javax.persistence.EntityManagerFactory;
import java.util.Arrays;
import java.util.List;

@Configuration
public class BeanConfiguration {

    @Bean(value = "wxMaAccessFacade")
    @ConditionalOnProperty(value = "wx.ma.enabled", havingValue = "true")
    public AccessFacade accessFacade(WxMaService wxMaService) {
        return new DefaultAccessFacade(wxMaService);
    }

    @Bean
    public TokenStore tokenStore(
            @Value(value = "${dify.base-url}") String baseUrl,
            @Value(value = "${dify.console.username}") String username,
            @Value(value = "${dify.console.password}") String password,
            ConnectionPool connectionPool
    ) {
        return new TokenStore(new LoginFacade(baseUrl, username, password, connectionPool));
    }

    @Bean
    public ConsoleFacade consoleFacade(
            @Value(value = "${dify.base-url}") String baseUrl,
            ConnectionPool connectionPool,
            TokenStore tokenStore
    ) {
        return new ConsoleFacade(baseUrl, connectionPool, tokenStore);
    }

    @Bean
    public DatasetFacade datasetFacade(
            @Value(value = "${dify.base-url}") String baseUrl,
            @Value(value = "${dify.api.key}") String apiKey,
            ConnectionPool connectionPool
    ) {
        return new DatasetFacade(baseUrl, apiKey, connectionPool);
    }

    @Bean
    @ConditionalOnMissingBean(value = IWebSearchService.class)
    public IWebSearchService bingSearchFacade(
            @Value(value = "${bing.search.base-url}") String baseUrl,
            @Value(value = "${bing.search.api-key}") String subscriptionKey,
            ConnectionPool connectionPool
    ) {
        return new BingSearchFacade(baseUrl, subscriptionKey, connectionPool);
    }

    @Bean
    public List<IPlugin> plugins(
            @Value(value = "${bing.search.count}") int searchCount,
            @Lazy ConversationRepository<? extends Conversation> conversationRepository,
            AbstractMediaRepository mediaRepository,
            IWebSearchService webSearchService
    ) {
        return Arrays.asList(new ParserPlugin(conversationRepository, mediaRepository), new WebSearchPlugin(searchCount, webSearchService));
    }

    @Bean
    public ModelRepository<?> modelRepository(
            @Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
            @Value(value = "${llm.model.debug}") boolean debug
    ) {
        return new ModelRepository<>(entityManagerFactory, jinqJPAStreamProvider, Model.class, debug);
    }

    @Bean
    public MemoryRepository<?> memoryRepository(
            @Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
            @Value(value = "${llm.memory.max-size}") Integer memorySize,
            @Value(value = "${llm.memory.redis.key-prefix}") String redisKeyPrefix,
            @Lazy ConversationRepository<? extends Conversation> conversationRepository,
            AbstractMediaRepository mediaRepository,
            RedisConnectionFactory redisConnectionFactory
    ) {
        return new MemoryRepository<>(entityManagerFactory, jinqJPAStreamProvider, Memory.class, memorySize, redisKeyPrefix, conversationRepository, mediaRepository, redisConnectionFactory);
    }

    @Bean
    public PageRepository pageRepository(
            @Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
            @Lazy PermissionRepository permissionRepository
    ) {
        return new DefaultPageRepository(entityManagerFactory, jinqJPAStreamProvider, permissionRepository);
    }

    @Bean
    public UserRepository<?> userRepository(
            @Value(value = "${sec.password-constraint}") String passwordConstraint,
            @Value(value = "${sec.rsa.default.private-key}") String rsaPrivateKey,
            @Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
            ConversationRepository<? extends Conversation> conversationRepository,
            MemoryRepository<? extends Memory> memoryRepository,
            AbstractMediaRepository mediaRepository,
            com.chinamobile.sparrow.domain.service.wx.ma.AccessFacade accessFacade
    ) {
        return new DefaultUserRepository(entityManagerFactory, jinqJPAStreamProvider, conversationRepository, memoryRepository, mediaRepository, passwordConstraint, rsaPrivateKey, accessFacade);
    }

    @Bean
    public StatisticService statisticService(
            @Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
            UserRepository<?> userRepository
    ) {
        return new DefaultStatisticService(entityManagerFactory, jinqJPAStreamProvider, userRepository);
    }

    @Bean
    public LoginController loginController(
            @Value(value = "${sec.captcha}") boolean captcha,
            @Value(value = "${sec.rsa.default.public-key}") String rsaPublicKey,
            @Value(value = "${sec.rsa.default.private-key}") String rsaPrivateKey,
            VerificationCodeRepository verificationCodeRepository,
            com.chinamobile.sparrow.domain.service.wx.ma.AccessFacade wxMaAccessFacade,
            LoginUtil loginUtil
    ) {
        return new DefaultLoginController(captcha, rsaPublicKey, rsaPrivateKey, verificationCodeRepository, null, null, null, wxMaAccessFacade, loginUtil);
    }

    @Bean
    public ProfileController profileController(
            DefaultUserRepository userRepository,
            LoginUtil loginUtil
    ) {
        return new DefaultProfileController(userRepository, loginUtil);
    }

    @Bean
    public ModelController modelController(
            ModelRepository<? extends Model> modelRepository,
            LoginUtil loginUtil
    ) {
        return new ModelController(modelRepository, loginUtil);
    }

    @Bean
    public DocumentController documentController(
            @Value(value = "${llm.conversation.document.accept}") String accept,
            ConversationRepository<? extends Conversation> conversationRepository,
            AbstractMediaRepository mediaRepository,
            LoginUtil loginUtil
    ) {
        return new DocumentController(accept, conversationRepository, mediaRepository, loginUtil);
    }

    @Bean
    public PluginController pluginController(
            @Value(value = "${cognitive.speech.key}") String key,
            @Value(value = "${cognitive.speech.region}") String region,
            ConsoleFacade consoleFacade
    ) {
        return new PluginController(key, region, consoleFacade);
    }

    @Bean
    public SettingController settingController(
            @Value(value = "${sec.rsa.default.public-key}") String rsaPublicKey,
            DefaultUserRepository userRepository,
            DepartmentRepository<?> departmentRepository,
            RoleRepository roleRepository,
            PermissionRepository permissionRepository,
            PageRepository pageRepository,
            LoginUtil loginUtil
    ) {
        return new DefaultSettingController(rsaPublicKey, userRepository, departmentRepository, roleRepository, permissionRepository, pageRepository, loginUtil);
    }

    @Bean
    public ReaderController readerController(
            AbstractMediaRepository mediaRepository,
            RecordController recordController,
            LoginUtil loginUtil
    ) {
        return new DefaultReaderController(mediaRepository, recordController, loginUtil);
    }

    @Bean
    public RecordController recordController(
            AbstractMediaRepository mediaRepository,
            LoginUtil loginUtil
    ) {
        return new DefaultRecordController(mediaRepository, loginUtil);
    }

    @Bean
    public UserController userController(
            DefaultUserRepository userRepository,
            LoginUtil loginUtil
    ) {
        return new DefaultUserController(userRepository, loginUtil);
    }

}