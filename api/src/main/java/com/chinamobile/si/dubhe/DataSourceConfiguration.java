package com.chinamobile.si.dubhe;

import com.chinamobile.sparrow.domain.infra.orm.jinq.PostgreSQLFunctions;
import com.zaxxer.hikari.HikariDataSource;
import org.apache.shardingsphere.driver.api.yaml.YamlShardingSphereDataSourceFactory;
import org.hibernate.SessionFactory;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.orm.hibernate5.HibernateTransactionManager;
import org.springframework.orm.hibernate5.LocalSessionFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.util.ResourceUtils;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import javax.sql.DataSource;
import java.io.File;
import java.io.IOException;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.Date;
import java.util.Properties;

@Configuration
public class DataSourceConfiguration {

    @Bean
    @ConfigurationProperties(prefix = "spring.datasource.main")
    public DataSource mainDataSource() {
        return new HikariDataSource();
    }

    // @Bean
    public DataSource mainDataSource(@Value(value = "${spring.datasource.main.config}") String path) throws IOException, SQLException {
        File _yaml = ResourceUtils.getFile(path);
        return YamlShardingSphereDataSourceFactory.createDataSource(_yaml);
    }

    @Bean
    @ConfigurationProperties(prefix = "spring.datasource.main.properties")
    public Properties mainDataSourceHibernateProperties() {
        return new Properties();
    }

    @Bean
    public LocalSessionFactoryBean mainSessionFactory(@Value(value = "${spring.datasource.main.packages}") String packages, @Qualifier(value = "mainDataSource") DataSource dataSource, @Qualifier(value = "mainDataSourceHibernateProperties") Properties properties) {
        LocalSessionFactoryBean _sessionFactoryBean = new LocalSessionFactoryBean();
        _sessionFactoryBean.setDataSource(dataSource);

        String[] _packages = StringUtils.hasLength(packages)
                ? Arrays.stream(packages.split(","))
                .map(String::trim)
                .toArray(String[]::new)
                : new String[]{"com.chinamobile.sparrow.domain.model"};
        _sessionFactoryBean.setPackagesToScan(_packages);

        _sessionFactoryBean.setHibernateProperties(properties);

        return _sessionFactoryBean;
    }

    @Bean
    @Primary
    public PlatformTransactionManager mainTransactionManager(@Qualifier(value = "mainSessionFactory") SessionFactory sessionFactory) {
        return new HibernateTransactionManager(sessionFactory);
    }

    @Bean
    public JinqJPAStreamProvider mainJinqJPAStreamProvider(@Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory) throws NoSuchMethodException {
        JinqJPAStreamProvider _provider = new JinqJPAStreamProvider(entityManagerFactory.createEntityManager().getMetamodel());

        _provider.registerCustomSqlFunction(PostgreSQLFunctions.class.getDeclaredMethod("dateFormat", Date.class, String.class), "dateFormat");
        _provider.registerCustomSqlFunction(PostgreSQLFunctions.class.getDeclaredMethod("datePart", String.class, Date.class), "datePart");

        return _provider;
    }

}